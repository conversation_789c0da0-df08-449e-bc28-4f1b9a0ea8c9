# User Stories

## User Story 1: Reporting Reliabilities

- As a Reevo engineer, I want to publish changes to the reporting charts reliably
- Acceptance criteria
    - The changes to the datasets should be tracked in our codebase and be reviewed before they are published
    - The changes should be tested in their own so we know it won’t break the existing report
    - The changes to the queries should be synced to the Supset dataset automatically to avoid human error
    - There should be tests against the Superset datasets to ensure the it works end to end.

## User Story 2: Extended Filter to support

- As a Reevo user, I want to apply more filters to the current reporting charts
- Acceptance criteria
    - Support the MUST_HAVE filters in the spreadsheet
        - https://docs.google.com/spreadsheets/d/10go13ftvM6AwFMXvCFwejQmOXWM4Pr5fL2lg0DTHpfU/edit?usp=sharing

## User Story 3: Activity Trend Report

- As a Reevo user, I want to see the top funnel and mid funnel activity metrics over time
- AS a sales team manager, I want to break down the reports by the rep so I can see the overall team performance
- Acceptance criteria
    - Support top funnel metrics: the number of sequence emails sent out to the cotnacts
    - Support mid funnel metrics: the number phone calls and meeting scheduled
    - The report should be able to break down by rep

## User Story 4: Demo Data

- As reevo GTM team member, I want to have charts that I can demo to the potential customers
- Acceptance criteria
    - Can create a dedicated organization with demo data ingested
    - The GTM team should not need to maintain these data
    - The charts should be fully functional in the testing org so the customers can see all the charts we offered

# Research

## Part I: Extended Filtering

- https://docs.google.com/spreadsheets/d/10go13ftvM6AwFMXvCFwejQmOXWM4Pr5fL2lg0DTHpfU/edit?usp=sharing

HubSpot’s deal-stage reporting leverages both **core Deal object properties** (the “deal stage data model”) and **supplemental/derived properties** that are pulled in from various parts of the HubSpot CRM (Contacts, Companies, marketing analytics, forecasting tools, etc.). Here’s a more detailed breakdown:

---

### 1. **Core (Native) Deal Properties**

These are properties that exist natively on HubSpot’s “Deal” object. Examples include:

- **Deal name**
- **Deal owner**
- **Deal stage** / **Deal stage probability**
- **Pipeline**
- **Amount**
- **Close date**
- **Create date**

These fields are either:

1. **Manually entered** by users (e.g., “Deal name,” “Deal owner”), or
2. **Automatically managed** by HubSpot’s system (e.g., “Create date,” “Last modified date”).

Properties such as **“Days to close,” “Time in current stage,” “Is closed won”** are also derived by HubSpot *based on* these native fields, but the underlying data originates in Deal history and pipeline stage changes.

---

### 2. **Lifecycle / Funnel-Derived Properties**

Properties like:

- **Date entered X stage (all available stages)**
- **Date exited X stage (all available stages)**
- **Cumulated time in X stage (all available stages)**
- **Last time in X stage (all available stages)**

are generated by HubSpot’s **funnel analytics**. HubSpot automatically tracks when a Deal enters or exits each stage in your pipeline. From that, the system can compute time spent in stage, date/time of stage changes, and so on. These “stage-tracking” fields do not typically appear in the standard Deal property list until you start using funnel reporting; HubSpot dynamically populates them based on pipeline stage history.

---

### 3. **Forecasting & Weighted Amount Properties**

Several properties relate to **forecasting** or **weighted deal amounts**, for example:

- **Forecast amount**
- **Forecast category**
- **Forecast probability**
- **Weighted amount** / **Weighted amount in company currency**

These come from **HubSpot’s forecasting tools** (in Sales Hub Professional/Enterprise tiers). You can configure your pipeline and forecast categories, and HubSpot calculates forecasting and weighted amounts based on the deal amount multiplied by a probability (tied to the stage probability or a custom forecast probability).

---

### 4. **Recurring Revenue / Subscription-Based Properties**

- **Monthly recurring revenue**
- **Annual recurring revenue**
- **HubSpot Shared Deal MRR** / **MRR Currency Code**
- **Recurring revenue amount**
- **Recurring revenue deal type**
- **Recurring revenue inactive date** / **inactive reason**

These can come from **HubSpot’s “Recurring Revenue” tracking features** (used when your business sells subscriptions or renewable contracts). You might see them if you’ve enabled recurring revenue analytics or use a subscription-based workflow. Sometimes these are standard in certain HubSpot portals, other times they’re custom fields created to track MRR/ARR.

---

### 5. **Associated Records & Cross-Object Properties**

- **Associated contact**
- **Primary associated company**
- **Number of associated contacts**
- **Associated Shared Deal Product Interests** (line items / products)

HubSpot’s CRM allows you to associate Deals with Contacts, Companies, and Products (line items). These filters pull from **the related records** in your HubSpot database. For instance, “Associated contact” is referencing the contact(s) linked to that Deal. “Number of associated contacts” or “Number of associated line items” is **calculated automatically** based on those associations.

---

### 6. **Marketing & Analytics-Derived Properties**

- **Latest Traffic Source** (and the variant “Latest Traffic Source XXX”)
- **Original traffic source** (and the variant “Original traffic source XXX”)
- **Campaign of last booking in meetings tool**
- **Medium of last booking in meetings tool**
- **Source of last booking in meeting tool**

These fields are typically drawn from **HubSpot’s Marketing analytics** or the **Meetings tool**. They track how a contact (later associated with the Deal) arrived on your site or how the last meeting was booked (which campaign, source, or channel). As soon as a contact is associated with the Deal, HubSpot can attribute those contact-level marketing details at the Deal level for reporting.

---

### 7. **Team and Ownership Properties**

- **Attributed reporting team**
- **Business Units**
- **HubSpot Team**
- **Shared teams / Shared users**

These relate to how HubSpot organizes users into teams (e.g., for round-robin assignments or forecasting by team). “Business Units” is another layer some portals use to separate data by brand or region.

---

### 8. **System or Auto-Calculated Properties**

- **Last Activity Date** / **Next activity date**
- **Number of Sales Activities** / **Number of times contacted**
- **Last Contacted**
- **Last modified date**
- **Owner assigned date**

HubSpot calculates these automatically by looking at the timeline of activities logged on the Deal (calls, emails, tasks, etc.). For instance, **Last Activity Date** updates whenever a call or email or meeting is logged on that Deal record.

---

### 9. **User-Created or Integration Properties**

Some fields in your list can also be **custom properties** that your organization (or an integration) introduced. For example:

- **Deal pushed**
- **Priority**
- **Deal Score**
- **Closed lost reason** / **Closed won reason** (sometimes standard, sometimes custom)

These may be part of a standard property set in certain HubSpot tiers, or they could be custom definitions. “Deal Score” might be a custom scoring approach your team built, while “Closed lost reason” could be a dropdown property that you manually populate or tie to a workflow.

---

## Where They Come From, Summarized

1. **Core Deal Model**: Standard HubSpot Deal fields like amount, stage, owner, and stage probability.
2. **Funnel & Lifecycle Tracking**: Automatically generated timeline properties (dates entered/exited stages, time in stage).
3. **Forecasting / Weighted Amount**: Derived from pipeline configuration and forecast tools.
4. **Recurring Revenue**: Part of HubSpot’s subscription/recurring revenue features (if enabled).
5. **Associated Record Counts**: HubSpot automatically populates these by checking linked records (Contacts, Companies, Products).
6. **Marketing Analytics**: Original/Latest source, campaign, UTM parameters—pulled from the contact-level marketing data once a Deal is associated with a Contact.
7. **Team & Ownership**: System properties for dividing deals by teams.
8. **System (Auto-Calculated)**: Activity dates, number of calls/emails, last contacted—driven by the CRM’s timeline.
9. **Custom / Integration**: Fields introduced by your organization or through third-party integrations (could be specifically named like “Deal pushed,” “Priority,” etc.).

---

# Part II: Custom Field Support

- **As a RevOps user, I want to create custom reports using custom fields and objects, so that I can gain insights specific to my business needs**. This addresses the limitation in HubSpot where reporting is only effective with pre-designed templates.
- **As a sales leader, I want the system to perform calculations for conversions (numerator/denominator) using custom stages and date stamps, so that I can accurately measure and analyze sales performance**. This addresses the issue where HubSpot struggles with math for conversions if you're not using their out-of-the-box stages.
- **As a RevOps user, I want custom formula fields to not break the reporting functionality, so that I can use them without compromising the integrity of my data analysis**. This ensures that the introduction of custom formulas doesn't disrupt the entire reporting functionality.
- **As an administrator, I want to easily organize and integrate records from various sources (e.g., website submissions), and match them to existing contacts and accounts, so that I can maintain a clean and accurate database**. This tackles the challenges related to HubSpot's relationship-based database, which makes it difficult to properly organize and integrate records.
- **As a RevOps user, I want the ability to use a lightweight Business Intelligence (BI) tool (e.g., Google Studio) with Revo, so that I can create custom reports and dashboards without being limited by the built-in reporting functionality**.
- **As a RevOps user, I want error alerts and logs that show when something is not syncing and why, so that I can quickly identify and resolve integration issues**.
- **As a RevOps user, I want to be able to control the direction of the sync (one-way or bi-directional) between Revo and other systems (e.g., HubSpot), so that I can manage data flow according to my specific needs**.
- **As a RevOps user, I want to prevent new data from overwriting existing data in specific fields, so that important information is preserved**.
- As a rev ops person, I want the ability to customize what types of fields are brought over, so that all the standard fields sync properly.
- As a rev ops person, I want logic in the system, for example, if the data is present, give the option to update it or not update it, so that there can be field level control.
- As a rev ops user, I want a system that alerts me at the time something breaks down so I can proactively fix it.

## HubSpot Report Feedback from Wennsheen

### Part 1: Essential Tools for the sales team

Wensheen describes a number of reports that are essential for a sales team. Here's a breakdown of these reports, starting from the bottom of the funnel and working upwards:

- **Closed Deals:**
    - How much has been closed?
    - How long did it take to close deals, measured in days from creation to close (won or lost)?
    - How much time was spent in each stage?
    - What is the quota attainment on a per-rep basis?
    - What is the lead source for closed deals?
    - What is the revenue generated by those deals?
- **Pipeline Coverage:**
    - What is the overall close rate?
    - What is the distribution of the pipeline overall, broken down by stage, close date, and rep?
- **Lost Deals:**
    - Which deals are being lost?
    - What are the reasons for deals being lost?
- **Forecast:**
    - What is the current projected finish for the quarter?
    - How much is left to close?
    - What actions are needed to close the gap?
- **Pipeline Generation Efficiency:**
    - How many opportunities are being created?
    - How efficient is the pipeline generation?
    - What sequences are performing best to generate opportunities?
    - Are reps performing the required number of activities (calls, emails, LinkedIn) on a daily or weekly basis?

In addition to these reports, Wensheen also emphasizes the importance of looking at **trends over time**. This includes tracking metrics such as:

- Closed won business
- Pipeline generation
- New logo growth
- Number of opportunities created per rep per month
- Average deal size
- ARR (Annual Recurring Revenue) versus one-time revenue
- Pipeline conversion

The goal of these reports is to understand what results were achieved, what drove those results, and what changes can be made to improve future outcomes.

- What are the results
- What drove those results
- what you can do to change the results

### Part II: regarding BI tools

Wensheen's point about Business Intelligence (BI) tools is that they are often adopted because regular reporting tools can't deliver the desired functionality. She believes that if you can figure out the core questions that need answers and build enough flexibility for people to use the BI tool, that may be enough. She notes that Hubspot has done this, providing basic sales analytics, that is better than Salesforce.

According to Wensheen, the ideal solution for overcoming the challenge of limited cross-object data referencing would be a data warehouse allowing more comprehensive data analysis across sales, marketing, customer success, and product data.

Wensheen notes that she has been talking to Omni and Canvas, and she has heard that Equals is great.

### Part III: regarding Cross Data Object Reference

Wensheen emphasizes the significance of **cross-object data referencing** in CRM systems, which refers to the ability to easily access and analyze data across different objects (e.g., contacts, accounts, deals) to gain a comprehensive understanding of business processes.

Here's a summary of Wensheen's key points regarding cross-object data referencing:

- **Difficulty in current systems:** Wensheen notes that a major challenge is the difficulty in getting a unified view of data across different objects in CRM systems like Hubspot and Salesforce. This often requires manually pulling data from multiple objects and layering them in tools like Excel, which is time-consuming and inefficient.
- **Reporting limitations:** Current reporting tools often have limitations in how many objects can be referenced and require users to download and merge data manually.
- **Ideal solution**: The ideal solution would be a data warehouse that allows for more flexible and comprehensive data analysis across sales, marketing, customer success, and product data.
- **Benefits of improved cross-object referencing**:
    - **Better understanding of transaction outcomes**: Cross-object data referencing enables users to understand the leading indicators and data points that impact transaction outcomes.
    - **Improved marketing attribution**: With cross-object referencing, marketers can determine the ROI of different marketing programs.
    - **Enhanced sales and marketing alignment**: Cross-object referencing facilitates the calculation of ROI for both sales and marketing efforts.
    - **Better insights into deal cycles**: Cross-object data referencing enables businesses to identify factors impacting their deal cycles, such as competitive pressure.
- **Need for better out-of-the-box reporting:** Wensheen praises Hubspot for providing some basic sales analytics out of the box, but notes that Salesforce lacks these features. A well-designed system should provide intuitive filters and make it easier to answer core business questions.

Wensheen's experience shows that effectively joining data from different sources is essential for answering important business questions and making informed decisions.

## HubSpot Reporting Discussion /w Clarus

The discussion participants highlight several specific problems with HubSpot's reporting system and suggest potential solutions.

Details of their discussion regarding HubSpot's reporting system:

- **Limitations with Customization**: HubSpot reporting is only effective when using its pre-designed templates. The dashboards and reports become "essentially useless" when custom fields or objects are introduced.
- **Difficulty with Conversion Calculations**: HubSpot cannot perform necessary calculations for most conversions if you don't use their out-of-the-box stages. Specifically, it cannot divide a numerator by a denominator.
- **Formula Field Issues**: Custom formula fields often disrupt the entire reporting functionality.
- **Relationship-Based Database**: HubSpot is a relationship-based database, which makes it challenging to organize and integrate records properly, especially when data is submitted through a website and needs to match existing contacts and accounts.

Potential solutions and alternatives mentioned:

- **Lightweight BI Tools**: It was suggested to use a lightweight Business Intelligence (BI) tool, such as Google Studio, to take the numerator and denominator and create the desired funnel reports.
- **Custom-Built Reports**: A Rev Ops team can build completely custom reports, which is what the discussion participants said that HubSpot themselves did.
- **Early Communication About Limitations**: Setting expectations early with pre-seed companies that fancy reports will not be possible, but a numerator and denominator can be provided to figure out externally.

## Link

- https://notebooklm.google.com/notebook/92780ca6-52bb-4d5d-afc9-7c32f1887ca7