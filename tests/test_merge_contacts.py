import asyncio
from uuid import UUID, uuid4

from salestech_be.core.crm_integrity.integrity_operation_workflows.merge_contacts_workflow import (
    MergeContactsWorkflow,
)
from salestech_be.core.crm_integrity.types.activity_params import (
    MergeContactsParam,
)
from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
from salestech_be.task.instantiate.database import get_db_engine


async def merge_contacts() -> None:
    db_engine = await get_db_engine()
    client = await get_temporal_client()
    job_id = uuid4()
    param = MergeContactsParam(
        job_id=job_id,
        src_contact_id=UUID("cfc9e781-ed62-47de-b541-712a7955eedc"),
        dest_contact_id=UUID("648f2e5e-4b98-4ee5-9d0d-c73535e7430b"),
        user_id=UUID("36ad3654-0978-41f6-b25d-51915d47a401"),
        organization_id=UUID("1148558e-b267-4942-87f6-64bd39e479fe"),
    )

    # Execute a workflow asynchronously
    handle = await client.start_workflow(
        MergeContactsWorkflow.run,
        args=param,
        task_queue=DEFAULT_TASK_QUEUE,
    )

    print(f"Started workflow. Workflow ID: {handle.id}, RunID {handle.result_run_id}")
    result = await handle.result()
    print(f"Result: {result}")


if __name__ == "__main__":
    asyncio.run(merge_contacts())
