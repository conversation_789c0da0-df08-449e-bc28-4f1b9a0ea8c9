import asyncio
from uuid import UUID, uuid4

from salestech_be.core.workflow.types.schema import PrepareExecuteNodeParam
from salestech_be.db.models.workflow import (
    WorkflowBlockName,
    WorkflowNode,
    WorkflowNodePosition,
    WorkflowNodeType,
)
from salestech_be.task.instantiate.database import get_db_engine
from salestech_be.tests.integration.conftest import workflow_repo
from salestech_be.tests.integration.core.workflow.wokflow_execution_test_schemas import (
    mock_send_slack_message_action_node_input_schema,
    mock_send_slack_message_action_node_output_schema,
)
from salestech_be.tests.util.factories import WorkflowNodeFactory
from salestech_be.util.time import zoned_utc_now


async def send_slack_message() -> None:
    db_engine = await get_db_engine()

    send_slack_message_action_node_id = uuid4()
    user_id = uuid4()
    organization_id = uuid4()

    mock_send_slack_message_action_node_name = "send_slack_message_action_10021"
    from salestech_be.core.workflow.activities.send_slack_message_node_activity import (
        SendSlackMessageNodeActivity,
    )

    workflow_node = WorkflowNode(
        id=send_slack_message_action_node_id,
        name=mock_send_slack_message_action_node_name,
        type=WorkflowNodeType.ACTION,
        workflow_id=uuid4(),
        snapshot_id=uuid4(),
        block_id=UUID("592b65c2-7a76-48e8-b1e5-************"),
        block_name=WorkflowBlockName.send_slack_message_action,
        input_schema=mock_send_slack_message_action_node_input_schema,
        output_schema=mock_send_slack_message_action_node_output_schema,
        position=WorkflowNodePosition(x=0, y=0),
        owner_user_id=user_id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )

    result = await SendSlackMessageNodeActivity(db_engine=db_engine).run(
        param=PrepareExecuteNodeParam(
            node_to_execute=workflow_node,
            context={}
        )
    )
    print(result)


if __name__ == "__main__":
    asyncio.run(send_slack_message())
