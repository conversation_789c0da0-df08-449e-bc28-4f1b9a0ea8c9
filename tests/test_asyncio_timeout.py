import asyncio


async def delay(delay_seconds: int) -> int:
    print(f'sleeping for {delay_seconds} second(s)')
    await asyncio.sleep(delay_seconds)
    print(f'finished sleeping for {delay_seconds} second(s)')
    return delay_seconds

async def main():
    awaitables = [
        asyncio.create_task(delay(3)),
        delay(3),
    ]
    for awaitable in awaitables:
        print("task", type(awaitable))
        try:
            # result = await asyncio.wait_for(asyncio.shield(task), 1)
            result = await asyncio.wait_for(awaitable, 1)
            print(result)
        except TimeoutError:
            pass


asyncio.run(main())
