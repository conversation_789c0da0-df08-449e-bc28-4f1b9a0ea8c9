from fastapi import <PERSON><PERSON><PERSON>
import asyncio, random

import asyncio
from functools import wraps
from typing import Awaitable, Callable, ParamSpec, TypeVar

from fastapi import HTTPException, Request, Response
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse, UJSONResponse
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.status import HTTP_504_GATEWAY_TIMEOUT



TIMEOUT_MESSAGE = "Request timed out"
TIMEOUT_STATUS_CODE = HTTP_504_GATEWAY_TIMEOUT
TIMEOUT_URL_PATH_TIMEOUT_SECONDS = {
    # "/api/v1/meetings/_list": 0.5,
    "/api/v1/contacts/_list": 10,
    "/api/v1/accounts/_list": 10,
    "/api/v1/pipelines/_list": 10,
    "/api/v1/tasks/_list": 10,
}


class TimeoutMiddleware(BaseHTTPMiddleware):
    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        if request.url.path in TIMEOUT_URL_PATH_TIMEOUT_SECONDS:
            try:
                return await asyncio.wait_for(
                    call_next(request),
                    timeout=TIMEOUT_URL_PATH_TIMEOUT_SECONDS[request.url.path],
                )
            except TimeoutError:
                # raise HTTPException(status_code=504, detail="Request timed out")
                return JSONResponse(
                    content={
                        "error_response_type": "GENERIC",
                        "error": "TimeoutError",
                        "message": TIMEOUT_MESSAGE,
                        "details": None,
                    },
                    status_code=TIMEOUT_STATUS_CODE,
                )
        else:
            return await call_next(request)

P = ParamSpec("P")
T = TypeVar("T")

def concurrent_task(
    key: str | None = None,
    limit: int = 1,
    ttl_ms: int = 900000,
    raise_on_failure: bool = False,
):
    """
    This function will return a decorator which will limit the num of concurrent running
    tasks using a redis counter
    @param key is name of counter which by default is the name of the task function
    @param limit is the max num of concurrent running tasks
    @param ttl_ms is the time in milliseconds that key lives in redis
    @param raise_on_failure will raise an RateLimitExceeded exception if running tasks
           num exceeds the limit. it is disabled by default and will NOT run the task
           by quitting silently

    usage:
    @cron_task_high(store_results=True, schedule=timedelta(minutes=1))
    @concurrent_task(limit=1)
    async def send_scheduled_messages()
       pass

    return a decorator
    """

    def decorator(
        target: Callable[P, Awaitable[T]],
    ) -> Callable[P, Awaitable[T | None]]:
        """
        return a new function to replace origin function
        """

        @wraps(target)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T | None:
            nonlocal key, limit, ttl_ms, raise_on_failure
            key = key or target.__name__
            print(f"ttl_ms: {ttl_ms}")
            try:
                return await target(*args, **kwargs)
                return await asyncio.wait_for(target(*args, **kwargs), timeout=ttl_ms)
            except TimeoutError:
                print("Request timed out in decorator")
                return JSONResponse(
                    content={"message": "Request timed out", "cache": False},
                    status_code=504,
                )
            except Exception as e:
                print(f"Error in decorator: {e}")
                return JSONResponse(
                    content={"message": "Error in decorator", "cache": False},
                    status_code=504,
                )
                # raise HTTPException(status_code=504, detail="Request timed out")
        return wrapper

    return decorator

app = FastAPI()

cache = {"result": random.random()}  # seed the cache


async def long_running_task():
    print("Starting long running task")
    await asyncio.sleep(10)  # sleep for 10 seconds
    print("Finished long running task")
    return random.random()  # return a random number


@app.get("/retrieve-data")
@concurrent_task(ttl_ms=1)
async def retrieve_data():
    try:
        # result = await asyncio.wait_for(
        #     long_running_task(), timeout=1
        # )  # timeout after five seconds of waiting
        result = await long_running_task()
        cache["result"] = result  # cache the result
        return {"message": result, "cache": False}
    except TimeoutError:
        print("Timed out!")
        return {"message": cache["result"], "cache": True}



# Run FastAPI app
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8002)