
import ast
import json
from typing import Dict


def convert_camel_case_to_snake_case(name: str):
    return "".join(["_" + c.lower() if c.isupper() else c.lower() for c in name]).lstrip("_")


def find_all_services_in_codebase() -> list[tuple[str, str]]:
    # find all services in the codebase using following command
    # git grep 'class ' salestech_be | grep 'Service'
    # Execute git grep command and parse output
    import subprocess

    cmd = "git grep 'class ' salestech_be | grep 'Service'"
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    services = set()
    # Parse each line of output
    for line in result.stdout.splitlines():
        parts = line.split(":")
        if len(parts) < 2:
            continue
        file_name = parts[0].strip()
        class_name = parts[1].replace("class ", "").strip()
        if "Service" not in class_name:
            continue
        services.add((file_name, class_name))

    return sorted(services)


def find_all_get_service_functions_in_codebase(services):
    # find all get_service_by_db_engine functions in the codebase
    get_service_functions = {}
    for file_name, class_name in services:
        get_service_functions.update(find_get_service_functions(file_name, class_name))
    return get_service_functions


def find_get_service_functions(file_name: str, class_name: str):
    # read the file and extract toplevel functions that return a service with class_name
    # eg: def get_xxx_service_by_db_engine(engine: DatabaseEngine) -> XxxService:
    # eg: def goal_query_service_by_request(request: Request) -> GoalQueryService:
    # eg: return {
    #   "get_xxx_service_by_db_engine": "XxxService",
    #   "goal_query_service_by_request": "GoalQueryService"
    # }
    
    # Read the file content
    with open(file_name, 'r') as f:
        file_content = f.read()

    # Parse the file with ast
    try:
        tree = ast.parse(file_content)
    except:
        return {}

    get_service_functions = {}

    # Look for top-level functions
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            # Check if function returns the target service class
            returns_service = False
            if node.returns:
                if isinstance(node.returns, ast.Name) and node.returns.id == class_name:
                    returns_service = True
                elif isinstance(node.returns, ast.Subscript):
                    if isinstance(node.returns.value, ast.Name) and node.returns.value.id == class_name:
                        returns_service = True

            # If function returns service, add to dict
            if returns_service:
                get_service_functions[node.name] = class_name

    return get_service_functions

    
def parse_dependencies_of_service(service_name: str, service_file: str):
    # find all dependencies of the service
    # read the file and extract __init__
    # parse the __init__ to find all dependencies of the service in 3 forms:
    # 1. notification_service: Annotated[
    #        NotificationService, Depends(get_notification_service_by_db_engine)
    #    ],
    # 2. notification_service = get_notification_service_by_db_engine(...)
    # 3. notification_service = NotificationService(...)
    import ast
    import re
    from typing import List, Tuple

    # Read the service file
    with open(service_file, 'r') as f:
        file_content = f.read()

    # Find the class definition
    class_def_pattern = f"class {service_name}[^:]*:"
    class_match = re.search(class_def_pattern, file_content)
    if not class_match:
        return []

    # Find the __init__ method within the class
    class_start = class_match.start()
    init_pattern = r"def __init__\s*\([^)]*\)\s*:"
    init_match = re.search(init_pattern, file_content[class_start:])
    if not init_match:
        return []

    # Parse the file with ast
    try:
        tree = ast.parse(file_content)
    except:
        return []

    dependencies = []

    # Helper to extract service name from type annotation
    def extract_service_from_annotation(annotation) -> str:
        if isinstance(annotation, ast.Name):
            return annotation.id
        elif isinstance(annotation, ast.Subscript):
            if isinstance(annotation.value, ast.Name) and annotation.value.id == 'Annotated':
                if isinstance(annotation.slice, ast.Tuple):
                    return annotation.slice.elts[0].id
                return annotation.slice.id
        return None

    # Find the class definition node
    class_node = None
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef) and node.name == service_name:
            class_node = node
            break

    if class_node:
        # Find __init__ method
        for node in class_node.body:
            if isinstance(node, ast.FunctionDef) and node.name == '__init__':
                # Look at arguments
                for arg in node.args.args[1:]: # Skip self
                    if hasattr(arg, 'annotation'):
                        service_name = extract_service_from_annotation(arg.annotation)
                        if service_name and service_name.endswith('Service'):
                            dependencies.append(service_name)

                # Look at assignments in body
                for stmt in node.body:
                    if isinstance(stmt, ast.Assign):
                        if isinstance(stmt.value, ast.Call):
                            if isinstance(stmt.value.func, ast.Name):
                                func_name = stmt.value.func.id
                                if func_name.endswith('Service'):
                                    dependencies.append(func_name)
                            elif isinstance(stmt.value.func, ast.Call):
                                if hasattr(stmt.value.func, 'func'):
                                    func_name = stmt.value.func.func.id
                                    if func_name.endswith('Service'):
                                        dependencies.append(func_name)

    return list(set(dependencies))

def find_service_import_other_service(service_name: str, service_file: str):
    # find service that import other services
    # read the file and extract all imports
    # eg: WorkflowNodeService import WorkflowGraphService,WorkflowValidationService
    # Parse the service file to find imported services
    with open(service_file, 'r') as f:
        tree = ast.parse(f.read())

    imported_services = []
    
    # Look for import statements
    for node in ast.walk(tree):
        if isinstance(node, ast.ImportFrom):
            for name in node.names:
                if name.name.endswith('Service'):
                    imported_services.append(name.name)
        elif isinstance(node, ast.Import):
            for name in node.names:
                if name.name.endswith('Service'):
                    imported_services.append(name.name)

    return list(set(imported_services))


def find_init_args_assignment_imports(
    service_name: str,
    service_file: str,
    get_service_functions: dict[str, str],
    services: list[tuple[str, str]]
):
    """
    Rewrites the __init__ method of a service class to use Annotated dependencies.
    Args:
        service_name: Name of the service class to modify
        service_file: Path to the service file
        get_service_functions: dict of get_service_by_db_engine functions
    """
    with open(service_file) as f:
        tree = ast.parse(f.read())

    # Find the service class
    for node in tree.body:
        if isinstance(node, ast.ClassDef) and node.name == service_name:
            # Find __init__ method
            for method in node.body:
                if isinstance(method, ast.FunctionDef) and method.name == "__init__":
                    # Extract repositories and services from assignments
                    repositories = []
                    services = []

                    for stmt in method.body:
                        if isinstance(stmt, ast.Assign):
                            if isinstance(stmt.value, ast.Call):
                                if isinstance(stmt.value.func, ast.Name):
                                    name = stmt.value.func.id
                                    if name.endswith('Repository'):
                                        repositories.append(name)
                                    elif name.endswith('Service'):
                                        services.append(name)

                    # find init method params(Repository and Service) not Annotated
                    #   eg: user_integration_repo: UserIntegrationRepository,
                    #       add UserIntegrationRepository to repositories
                    #   eg: user_calendar_schedule_service: UserCalendarScheduleService,
                    #       add UserCalendarScheduleService to services
                    # Find init params that are repositories or services
                    param_names = {}
                    for arg in method.args.args:
                        if hasattr(arg, 'annotation') and isinstance(arg.annotation, ast.Name):
                            param_type = arg.annotation.id
                            # record param_name
                            if param_type.endswith('Repository'):
                                param_names[arg.arg] = convert_camel_case_to_snake_case(param_type)
                                repositories.append(param_type)
                            elif param_type.endswith('Service'):
                                param_names[arg.arg] = convert_camel_case_to_snake_case(param_type)
                                services.append(param_type)
                    print(param_names)
                    # Build new __init__ signature with Annotated dependencies
                    new_args = []
                    if repositories:
                        new_args.append("# Repositories")
                        for repo in repositories:
                            param = f"{convert_camel_case_to_snake_case(repo)}: Annotated[{repo}, Depends()]"
                            new_args.append(param)
                    if services:
                        new_args.append("# Services") 
                        for svc in services:
                            param = f"{convert_camel_case_to_snake_case(svc)}: Annotated[{svc}, Depends()]"
                            new_args.append(param)

                    # Build assignments
                    # if Rvalue is assignment repo|svc
                    #   if Rvalue is a function,replace it with convert_camel_case_to_snake_case(get_service_functions[func_name])
                    #       get_service_functions = {
                    #         "get_sequence_service_by_db_engine": "SequenceService",
                    #       }
                    #       before:self.sequence_service = get_sequence_service_by_db_engine(engine=engine)
                    #       after: self.sequence_service = convert_camel_case_to_snake_case(get_service_functions["get_sequence_service_by_db_engine"])
                    #   else Rvalue is a new instance, replace it with convert_camel_case_to_snake_case(svc|repo)
                    #       before: self.sequence_service = SequenceService(engine=engine)
                    #       after: self.sequence_service = convert_camel_case_to_snake_case(svc|repo)
                    # else if Rvalue is in param_names, replace it with param_names[Rvalue]
                    #   eg: 
                    #       before: self.calendar_account_repo = calendar_account_repo
                    #       after: self.calendar_account_repo = calendar_account_repo
                    # else keep the original assignment
                    # eg: self.logger = get_logger() keep the same
                    assignments = []
                    imports = []
                    for stmt in method.body:
                        if isinstance(stmt, ast.Assign):
                            if isinstance(stmt.value, ast.Call):
                                if isinstance(stmt.value.func, ast.Name):
                                    name = stmt.value.func.id
                                    if name.endswith('Repository') or name.endswith('Service'):
                                        # Case: self.x = Repository() or self.x = Service()
                                        target = stmt.targets[0]
                                        if isinstance(target, ast.Attribute):
                                            assignments.append(
                                                f"self.{target.attr} = {convert_camel_case_to_snake_case(name)}"
                                            )
                                    elif name in get_service_functions:
                                        # Case: self.x = get_service_by_db_engine()
                                        target = stmt.targets[0]
                                        if isinstance(target, ast.Attribute):
                                            service_name = get_service_functions[name]
                                            param = f"{convert_camel_case_to_snake_case(service_name)}: Annotated[{service_name}, Depends()]"
                                            new_args.append(param)
                                            # find service_name file path according to services
                                            for file_name, class_name in services:
                                                if class_name == service_name:
                                                    service_file = file_name
                                                    break
                                            # find service_name import path according to service_file
                                            from_path = service_file.replace("/", ".").replace(".py", "")
                                            import_path = f"from {from_path} import {service_name}"
                                            if "from fastapi import Depends" not in imports:
                                                imports.append("from typing import Annotated")
                                                imports.append("from fastapi import Depends")
                                            imports.append(import_path)

                                            assignments.append(
                                                f"self.{target.attr} = {convert_camel_case_to_snake_case(service_name)}"
                                            )
                                    else:
                                        # Keep original assignment for non-service/repo calls
                                        assignments.append(ast.unparse(stmt))
                            else:
                                # Keep original assignment for non-call values
                                assignments.append(ast.unparse(stmt))
                        else:
                            # Keep original non-assignment statements
                            assignments.append(ast.unparse(stmt))
                    return {
                        "args": new_args,
                        "assignments": assignments,
                        "imports": imports
                    }

    return None


def rewrite_init_args_assignments(
    service_name: str,
    service_file: str,
    content: dict[str,list]
):
    # content[args] add to init args
    # replace init body service and repository assignments with content[assignments]
    # rewrite the file
    import re
    from typing import Dict, List

    # Read the file content
    with open(service_file, 'r') as f:
        file_content = f.read()

    # Find the class definition and __init__ method
    class_def_pattern = f"class {service_name}[^:]*:"
    class_match = re.search(class_def_pattern, file_content)

    if not class_match:
        return

    # Find __init__ method
    init_pattern = r"\n\s*def\s+__init__\s*\("
    init_match = re.search(init_pattern, file_content[class_match.end():])
    if not init_match:
        return

    init_start = class_match.end() + init_match.start()
    
    # Find the end of __init__ by looking for next def or end of class
    
    next_def = re.search(r"\n\s*def|async\s+def\s+", file_content[init_start + init_match.end():])
    class_end = re.search(r"\n\s*class\s+", file_content[init_start + init_match.end():])
    
    if next_def:
        init_end = init_start + init_match.end() + next_def.start()
    elif class_end:
        init_end = init_start + init_match.end() + class_end.start()
    else:
        init_end = len(file_content)

    # Extract current init content
    init_content = file_content[init_start:init_end]
    print(init_content)
    # print(init_end)
    # print(file_content[init_end:])
    
    # Build new init signature
    new_args = ["self", "db_engine: DatabaseEngine"] + content["args"]
    new_init_sig = f"def __init__(\n        {',\n        '.join(new_args)}\n    ) -> None:"

    # Build new init body with assignments
    new_init_body = "\n        ".join(content["assignments"])
    # Replace old init with new version
    new_init = f"{new_init_sig}\n        {new_init_body}\n"
    new_content = file_content[:init_start]+"\n    " + new_init +"\n    " + file_content[init_end:]

    print(new_content)
    # Write back to file
    # with open(service_file, 'w') as f:
    #     f.write(new_content)


def construct_dag_from_service_dependencies(services: list[tuple[str, str]]):
    # construct a dag from service dependencies
    # each node is a service
    # each edge is a dependency
    # return a dict of service_name: [dependencies]
    # eg:
    #   {
    #     "ServiceA": ["ServiceB", "ServiceC"],
    #     "ServiceB": ["ServiceC"],
    #     "ServiceC": []
    #   }
    #   ServiceA depends on ServiceB and ServiceC
    #   ServiceB depends on ServiceC
    #   ServiceC has no dependencies
    pass

if __name__ == "__main__":
    from collections import Counter
    services = find_all_services_in_codebase()
    print(*services, sep="\n")
    quit()
    get_service_functions = find_all_get_service_functions_in_codebase(services)

    for service in services:
        # print(find_service_import_other_service(service[1], service[0]))
        # dependency_services = find_service_import_other_service(service[1], service[0])
        # last service change to annotated format
        if service[1] == "NylasWebhookService":
            print(service)
            content=find_init_args_assignment_imports(service[1], service[0], get_service_functions,services)
            print(json.dumps(content, indent=4))
            quit()
            rewrite_init_args_assignments(service[1],service[0],content)
