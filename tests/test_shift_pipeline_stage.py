
"""
stage (stage_id, re-map case) -> randomly set various stages, but ensure each stage have records (more on closed stages)
rep (owner_user_id) -> 10 users randomly, but ensure each user have pipelines
created_at (created_at) -> randomly set for the past 12 months
amount -> randomly set between 100000 - 5000000
"""

import asyncio
from uuid import UUID

from salestech_be.core.pipeline.service.pipeline_service import get_pipeline_service
from salestech_be.core.pipeline.service_api_schema import ShiftPipelineStageRequest
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.settings import settings

# ruff: noqa: S311


async def main() -> None:
    # list organizations or specify a org_id
    # check app_code of select list with this org
    # bootstrap select list by app_code
    db_engine = DatabaseEngine(url=str(settings.db_url))

    pipeline_service = get_pipeline_service(db_engine=db_engine)
    result = await pipeline_service.shift_pipeline_stage(
        organization_id=UUID("c6734584-b3f3-4608-b62e-a993f4703a8e"),
        user_id=UUID("36ad3654-0978-41f6-b25d-51915d47a401"),
        pipeline_id=UUID("1074d2f7-40f8-4f90-a1a9-e184db7f9500"),
        req=ShiftPipelineStageRequest(
            # target_stage_id=UUID("3d6a4489-b3ed-4bfa-a9f9-9b99deab40e4"), # Nurturing
            # target_stage_id=UUID("2dca2aa3-67c6-4dd6-a081-829632d0c8cf"), #Meeting Held
            target_stage_id=UUID("787c2225-091d-4f96-81aa-2f48a6a2ba81"), #Contract Out
            closed_reason_select_list_value_ids=[],
        ),
    )
    print(result)


if __name__ == "__main__":
    asyncio.run(main())
