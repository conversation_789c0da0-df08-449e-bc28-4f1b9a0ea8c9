from salestech_be.common.query_util.baseline_filter_extraction import BaselineFilterExtractor
from salestech_be.common.query_util.filter_schema import CompositeFilter, FilterSpec
from salestech_be.common.type.metadata.common import ObjectIdentifier
from salestech_be.common.type.metadata.schema import FieldReference, QualifiedField


filter_spec_contact = {
    "primary_object_identifier": {
        "object_kind": "STANDARD",
        "object_name": "meeting"
    },
    "filter": {
        "filter_type": "COMPOSITE",
        "all_of": [
        {
            "filter_type": "VALUE",
            "field": {
            "relationship_id": "meeting__to__invitee_contact",
            "field": {
                "path": [
                "id"
                ]
            }
            },
            "operator": "IN",
            "value": [
            "2395bbfd-37f4-43b7-b22e-997a15884f3b"
            ]
        },
        {
            "filter_type": "COMPOSITE",
            "any_of": [
            {
                "filter_type": "VALUE",
                "field": {
                "path": [
                    "starts_at"
                ]
                },
                "operator": "GT",
                "value": "2025-02-15T06:56:13.377Z"
            },
            {
                "filter_type": "VALUE",
                "field": {
                "path": [
                    "meeting_status"
                ]
                },
                "operator": "IN",
                "value": [
                "active"
                ]
            }
            ]
        }
        ],
        "none_of": [
        {
            "filter_type": "COMPOSITE",
            "all_of": [
            {
                "filter_type": "VALUE",
                "field": {
                "path": [
                    "meeting_platform"
                ]
                },
                "operator": "EQ",
                "value": "voice"
            },
            {
                "filter_type": "VALUE",
                "field": {
                "path": [
                    "is_recorded"
                ]
                },
                "operator": "EQ",
                "value": False
            }
            ]
        }
        ]
    }
}

filter_spec_account = {
    "primary_object_identifier": {
      "object_kind": "STANDARD",
      "object_name": "meeting"
    },
    "filter": {
      "filter_type": "COMPOSITE",
      "all_of": [
        {
          "filter_type": "VALUE",
          "field": {
            "relationship_id": "meeting__to__invitee_contact",
            "field": {
              "relationship_id": "contact__to__primary_account",
              "field": {
                "path": [
                  "id"
                ]
              }
            }
          },
          "operator": "EQ",
          "value": "def61d33-e25d-4318-b1f0-1d7772865337"
        },
        {
          "filter_type": "COMPOSITE",
          "any_of": [
            {
              "filter_type": "VALUE",
              "field": {
                "path": [
                  "starts_at"
                ]
              },
              "operator": "GT",
              "value": "2025-02-15T06:29:04.378Z"
            },
            {
              "filter_type": "VALUE",
              "field": {
                "path": [
                  "meeting_status"
                ]
              },
              "operator": "IN",
              "value": [
                "active"
              ]
            }
          ]
        }
      ],
      "none_of": [
        {
          "filter_type": "COMPOSITE",
          "all_of": [
            {
              "filter_type": "VALUE",
              "field": {
                "path": [
                  "meeting_platform"
                ]
              },
              "operator": "EQ",
              "value": "voice"
            },
            {
              "filter_type": "VALUE",
              "field": {
                "path": [
                  "is_recorded"
                ]
              },
              "operator": "EQ",
              "value": False
            }
          ]
        }
      ]
    }
}

filter_spec = filter_spec_account

filter_spec = FilterSpec(**filter_spec)

extractor = BaselineFilterExtractor()

field_reference = FieldReference(
    relationship_id="meeting__to__invitee_contact",
    field=QualifiedField(path=["id"])
)

field_reference = FieldReference(
    relationship_id="meeting__to__invitee_contact",
    field=FieldReference(
        relationship_id="contact__to__primary_account",
        field=QualifiedField(path=["id"])
    )
)

must_filters = extractor.extract_must_filter_by_field_reference(
    filter_spec=filter_spec,
    field=field_reference
)
print(must_filters)
