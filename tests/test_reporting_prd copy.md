# 用户故事

## 用户故事1：报告可靠性

- 作为Reevo工程师，我希望能够可靠地发布对报表图表的更改
- 验收标准
    - 数据集的更改应在我们的代码库中进行跟踪，并在发布前进行审查
    - 更改应该单独测试，以确保不会破坏现有报告
    - 查询的更改应自动同步到Supset数据集，以避免人为错误
    - 应该对Superset数据集进行测试，以确保端到端功能正常

## 用户故事2：扩展过滤器支持

- 作为Reevo用户，我希望能够对当前报表图表应用更多过滤器
- 验收标准
    - 支持电子表格中的MUST_HAVE过滤器
        - https://docs.google.com/spreadsheets/d/10go13ftvM6AwFMXvCFwejQmOXWM4Pr5fL2lg0DTHpfU/edit?usp=sharing

## 用户故事3：活动趋势报告

- 作为Reevo用户，我希望看到漏斗顶部和中部的活动指标随时间的变化
- 作为销售团队经理，我希望能按销售代表细分报告，以便查看整体团队表现
- 验收标准
    - 支持漏斗顶部指标：发送给联系人的序列邮件数量
    - 支持漏斗中部指标：电话和会议预约数量
    - 报告应能按销售代表进行细分

## 用户故事4：演示数据

- 作为Reevo GTM团队成员，我希望有可以向潜在客户演示的图表
- 验收标准
    - 可以创建一个专用组织并导入演示数据
    - GTM团队不需要维护这些数据
    - 图表在测试组织中应完全功能，以便客户可以看到我们提供的所有图表

# 研究

## 第一部分：扩展过滤

- https://docs.google.com/spreadsheets/d/10go13ftvM6AwFMXvCFwejQmOXWM4Pr5fL2lg0DTHpfU/edit?usp=sharing

HubSpot的交易阶段报告利用了**核心交易对象属性**（"交易阶段数据模型"）和从HubSpot CRM各个部分（联系人、公司、营销分析、预测工具等）提取的**补充/派生属性**。以下是详细分类：

---

### 1. **核心（原生）交易属性**

这些是HubSpot"交易"对象中原生存在的属性。例如：

- **交易名称**
- **交易所有者**
- **交易阶段** / **交易阶段概率**
- **管道**
- **金额**
- **结束日期**
- **创建日期**

这些字段要么是：

1. **用户手动输入**（如"交易名称"、"交易所有者"），或
2. **由HubSpot系统自动管理**（如"创建日期"、"最后修改日期"）。

诸如**"结束天数"、"当前阶段时间"、"是否成功结束"**等属性也是HubSpot*基于*这些原生字段派生的，但基础数据来自交易历史和管道阶段变更。

[继续翻译其余部分...]

---

### 2. **生命周期/漏斗派生属性**

属性如：

- **进入X阶段的日期（所有可用阶段）**
- **退出X阶段的日期（所有可用阶段）**
- **在X阶段的累计时间（所有可用阶段）**
- **最后一次在X阶段的时间（所有可用阶段）**

是由HubSpot的**漏斗分析**生成的。HubSpot会自动追踪交易在您的管道中进入或退出每个阶段的时间。基于这些数据，系统可以计算在每个阶段停留的时间、阶段变更的日期/时间等信息。这些"阶段追踪"字段通常不会在标准交易属性列表中显示，直到您开始使用漏斗报告；HubSpot会根据管道阶段历史记录动态填充这些字段。

### 3. **预测和加权金额属性**

几个与**预测**或**加权交易金额**相关的属性，例如：

- **预测金额**
- **预测类别**
- **预测概率**
- **加权金额** / **公司货币的加权金额**

这些来自**HubSpot的预测工具**（在Sales Hub Professional/Enterprise版本中）。您可以配置您的管道和预测类别，HubSpot根据交易金额乘以概率（与阶段概率或自定义预测概率相关）来计算预测和加权金额。

---

### 4. **循环收入/基于订阅的属性**

- **月度循环收入**
- **年度循环收入**
- **HubSpot共享交易MRR** / **MRR货币代码**
- **循环收入金额**
- **循环收入交易类型**
- **循环收入停用日期** / **停用原因**

这些可能来自**HubSpot的"循环收入"追踪功能**（用于销售订阅或可续约合同的企业）。如果您启用了循环收入分析或使用基于订阅的工作流，您可能会看到这些属性。有时这些是某些HubSpot门户的标准功能，有时它们是为追踪MRR/ARR而创建的自定义字段。

[继续翻译...]

### 5. **关联记录和跨对象属性**

- **关联联系人**
- **主要关联公司**
- **关联联系人数量**
- **关联共享交易产品兴趣**（明细项目/产品）

HubSpot的CRM允许您将交易与联系人、公司和产品（明细项目）关联。这些过滤器从HubSpot数据库中提取**相关记录**。例如，"关联联系人"是指与该交易关联的联系人。"关联联系人数量"或"关联明细项目数量"是基于这些关联**自动计算**的。

[继续翻译其余部分...]

[由于内容较长，我可以继续翻译剩余部分。请让我知道是否需要继续。]

### 6. **营销和分析衍生属性**

- **最新流量来源**（及其变体"最新流量来源XXX"）
- **原始流量来源**（及其变体"原始流量来源XXX"）
- **会议工具中最后预约的营销活动**
- **会议工具中最后预约的媒介**
- **会议工具中最后预约的来源**

这些字段通常来自**HubSpot的营销分析**或**会议工具**。它们追踪联系人（后来与交易关联）如何到达您的网站或最后一次会议是如何预约的（通过哪个营销活动、来源或渠道）。一旦联系人与交易关联，HubSpot就可以在交易层面归因这些联系人级别的营销详情用于报告。

---

### 7. **团队和所有权属性**

- **归因报告团队**
- **业务单位**
- **HubSpot团队**
- **共享团队/共享用户**

这些与HubSpot如何将用户组织成团队有关（例如，用于轮询分配或按团队预测）。"业务单位"是一些门户用来按品牌或地区分离数据的另一层。

---

### 8. **系统或自动计算属性**

- **最后活动日期** / **下一个活动日期**
- **销售活动数量** / **联系次数**
- **最后联系时间**
- **最后修改日期**
- **所有者分配日期**

HubSpot通过查看交易上记录的活动时间线（通话、邮件、任务等）自动计算这些内容。例如，**最后活动日期**在该交易记录上记录通话、邮件或会议时更新。

---

### 9. **用户创建或集成属性**

您列表中的一些字段也可能是您的组织（或集成）引入的**自定义属性**。例如：

- **交易推迟**
- **优先级**
- **交易得分**
- **关闭失败原因** / **关闭成功原因**（有时是标准的，有时是自定义的）

这些可能是某些HubSpot版本中的标准属性集的一部分，也可能是自定义定义。"交易得分"可能是您的团队建立的自定义评分方法，而"关闭失败原因"可能是您手动填充或与工作流关联的下拉属性。

---

## 数据来源总结

1. **核心交易模型**：标准HubSpot交易字段，如金额、阶段、所有者和阶段概率。
2. **漏斗和生命周期追踪**：自动生成的时间线属性（进入/退出阶段的日期，阶段停留时间）。
3. **预测/加权金额**：从管道配置和预测工具中派生。
4. **循环收入**：HubSpot订阅/循环收入功能的一部分（如果启用）。
5. **关联记录计数**：HubSpot通过检查关联记录（联系人、公司、产品）自动填充这些。
6. **营销分析**：原始/最新来源、营销活动、UTM参数—一旦交易与联系人关联，就从联系人级别的营销数据中提取。
7. **团队和所有权**：用于按团队划分交易的系统属性。
8. **系统（自动计算）**：活动日期、通话/邮件数量、最后联系—由CRM的时间线驱动。
9. **自定义/集成**：由您的组织或通过第三方集成引入的字段（可能特别命名为"交易推迟"、"优先级"等）。

[继续翻译下一部分...]

# 第二部分：自定义字段支持

- **作为RevOps用户，我希望能够使用自定义字段和对象创建自定义报告，以便获得特定于我的业务需求的洞察**。这解决了HubSpot中报告仅在使用预设模板时才有效的限制。
- **作为销售主管，我希望系统能够使用自定义阶段和日期戳执行转化计算（分子/分母），以便准确衡量和分析销售业绩**。这解决了当不使用HubSpot预设阶段时，无法进行转化率计算的问题。
- **作为RevOps用户，我希望自定义公式字段不会破坏报告功能，这样我就可以使用它们而不影响数据分析的完整性**。这确保了引入自定义公式不会破坏整个报告功能。
- **作为管理员，我希望能够轻松组织和整合来自各种来源的记录（例如，网站提交），并将它们与现有联系人和账户匹配，以便维护一个干净准确的数据库**。这解决了与HubSpot基于关系的数据库相关的挑战，这种数据库使得正确组织和整合记录变得困难。
- **作为RevOps用户，我希望能够在Revo中使用轻量级商业智能(BI)工具（例如Google Studio），这样我就可以创建自定义报告和仪表板，而不受内置报告功能的限制**。
- **作为RevOps用户，我希望有错误警报和日志，显示什么时候没有同步以及原因，这样我就可以快速识别和解决集成问题**。
- **作为RevOps用户，我希望能够控制Revo和其他系统（例如HubSpot）之间的同步方向（单向或双向），这样我就可以根据具体需求管理数据流**。
- **作为RevOps用户，我希望防止新数据覆盖特定字段中的现有数据，这样重要信息就能得到保留**。
- 作为运营人员，我希望能够自定义同步哪些类型的字段，这样所有标准字段都能正确同步。
- 作为运营人员，我希望系统中有逻辑，例如，如果数据存在，给出更新或不更新的选项，这样就可以进行字段级别的控制。
- 作为RevOps用户，我希望在出现问题时系统能够及时提醒我，这样我就能主动修复它。

## Wennsheen的HubSpot报告反馈

### 第一部分：销售团队的基本工具

Wensheen描述了销售团队必需的多个报告。以下是这些报告的细分，从漏斗底部向上：

- **已关闭的交易：**
    - 关闭了多少？
    - 关闭交易需要多长时间，从创建到关闭（成功或失败）的天数？
    - 在每个阶段花费了多少时间？
    - 每个销售代表的配额达成情况如何？
    - 已关闭交易的线索来源是什么？
    - 这些交易产生了多少收入？

[继续翻译...]

- **管道覆盖率：**
    - 整体成交率是多少？
    - 按阶段、结束日期和销售代表划分的整体管道分布是什么？
- **失败的交易：**
    - 哪些交易失败了？
    - 交易失败的原因是什么？
- **预测：**
    - 本季度当前的预计完成情况如何？
    - 还有多少需要完成？
    - 需要采取什么行动来缩小差距？
- **管道生成效率：**
    - 创建了多少商机？
    - 管道生成的效率如何？
    - 哪些序列在生成商机方面表现最好？
    - 销售代表是否完成了每日或每周所需的活动数量（电话、邮件、LinkedIn）？

除了这些报告外，Wensheen还强调了观察**随时间变化的趋势**的重要性。这包括追踪以下指标：

- 成功关闭的业务
- 管道生成
- 新客户增长
- 每个销售代表每月创建的商机数量
- 平均交易规模
- ARR（年度循环收入）与一次性收入的对比
- 管道转化率

这些报告的目标是了解：
- 取得了什么结果
- 是什么推动了这些结果
- 如何改变这些结果

### 第二部分：关于BI工具

Wensheen关于商业智能(BI)工具的观点是，人们经常采用它们是因为常规报告工具无法提供所需的功能。她认为，如果能够找出需要回答的核心问题，并为人们使用BI工具提供足够的灵活性，这可能就足够了。她指出Hubspot已经做到了这一点，提供的基础销售分析比Salesforce更好。

根据Wensheen的说法，克服有限的跨对象数据引用挑战的理想解决方案是建立一个数据仓库，允许对销售、营销、客户成功和产品数据进行更全面的分析。

Wensheen提到她一直在与Omni和Canvas交谈，并听说Equals很不错。

### 第三部分：关于跨数据对象引用

Wensheen强调了CRM系统中**跨对象数据引用**的重要性，这指的是能够轻松访问和分析不同对象（如联系人、账户、交易）之间的数据，以全面了解业务流程。

以下是Wensheen关于跨对象数据引用的主要观点：

- **当前系统的困难：** Wensheen指出，在Hubspot和Salesforce等CRM系统中，获取跨不同对象的统一数据视图是一个主要挑战。这通常需要手动从多个对象中提取数据并在Excel等工具中进行层叠，这既耗时又低效。
- **报告限制：** 当前的报告工具在可以引用的对象数量上往往有限制，需要用户手动下载和合并数据。
- **理想解决方案：** 理想的解决方案是建立一个数据仓库，允许对销售、营销、客户成功和产品数据进行更灵活和全面的分析。
- **改进跨对象引用的好处：**
    - **更好地理解交易结果：** 跨对象数据引用使用户能够理解影响交易结果的领先指标和数据点。
    - **改进营销归因：** 通过跨对象引用，营销人员可以确定不同营销项目的ROI。
    - **加强销售和营销协同：** 跨对象引用有助于计算销售和营销努力的ROI。
    - **更好地洞察交易周期：** 跨对象数据引用使企业能够识别影响其交易周期的因素，如竞争压力。
- **需要更好的开箱即用报告：** Wensheen赞扬Hubspot提供了一些基础的销售分析功能，但指出Salesforce缺乏这些功能。一个设计良好的系统应该提供直观的过滤器，使回答核心业务问题变得更容易。

Wensheen的经验表明，有效地连接来自不同来源的数据对于回答重要的业务问题和做出明智的决策至关重要。

[继续翻译最后一部分...]

## 与Clarus的HubSpot报告讨论

讨论参与者强调了HubSpot报告系统的几个具体问题并提出了潜在的解决方案。

关于HubSpot报告系统的讨论细节：

- **自定义限制**：HubSpot的报告只有在使用其预设模板时才有效。当引入自定义字段或对象时，仪表板和报告变得"基本上无用"。
- **转化率计算困难**：如果不使用他们的预置阶段，HubSpot无法执行大多数转化率所需的计算。具体来说，它无法进行分子除以分母的运算。
- **公式字段问题**：自定义公式字段经常会破坏整个报告功能。
- **基于关系的数据库**：HubSpot是一个基于关系的数据库，这使得正确组织和整合记录变得具有挑战性，特别是当数据通过网站提交并需要与现有联系人和账户匹配时。

提到的潜在解决方案和替代方案：

- **轻量级BI工具**：建议使用轻量级商业智能(BI)工具，如Google Studio，来获取分子和分母并创建所需的漏斗报告。
- **自定义构建报告**：讨论参与者提到RevOps团队可以构建完全自定义的报告，这正是HubSpot自己所做的。
- **早期沟通限制**：提前与种子前期公司沟通，说明复杂的报告可能无法实现，但可以提供分子和分母供外部计算。

## 链接

- https://notebooklm.google.com/notebook/92780ca6-52bb-4d5d-afc9-7c32f1887ca7