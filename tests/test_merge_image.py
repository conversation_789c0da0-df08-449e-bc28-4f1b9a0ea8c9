from PIL import Image
import os

def process_images(image_paths):
    # Load and crop all images
    cropped_images = []
    for path in image_paths:
        img = Image.open(path)
        # Crop from x=540 to end, full height
        cropped = img.crop((540, 0, img.width, img.height))
        cropped_images.append(cropped)

    def find_merge_position(img1, img2):
        # Convert images to RGB arrays for comparison
        arr1 = list(img1.convert('RGB').getdata())
        arr2 = list(img2.convert('RGB').getdata())
        
        width = img1.width
        
        # Check each row from bottom of img1 and top of img2
        for y1 in range(img1.height-1, 0, -1):
            row1 = arr1[y1*width:(y1+1)*width]
            for y2 in range(320, img2.height):
                row2 = arr2[y2*width:(y2+1)*width]
                if row1 == row2:
                    return y1, y2
                    
        return None

    # Recursively merge images
    result = cropped_images[0]
    for i in range(1, len(cropped_images)):
        merge_pos = find_merge_position(result, cropped_images[i])
        
        if merge_pos:
            y1, y2 = merge_pos
            # Create new image with merged height
            new_height = y1 + (cropped_images[i].height - y2)
            merged = Image.new('RGB', (result.width, new_height))
            
            # Paste first image
            merged.paste(result, (0, 0))
            
            # Paste second image starting from merge position
            merged.paste(cropped_images[i].crop((0, y2, cropped_images[i].width, cropped_images[i].height)), 
                        (0, y1))
            
            result = merged
        else:
            # If no matching rows found, just append vertically
            new_height = result.height + cropped_images[i].height
            merged = Image.new('RGB', (result.width, new_height))
            merged.paste(result, (0, 0))
            merged.paste(cropped_images[i], (0, result.height))
            result = merged

    return result

# Example usage:
image_paths = [
    '/Users/<USER>/Desktop/Screenshot 2025-01-09 at 20.43.38.png',
    '/Users/<USER>/Desktop/Screenshot 2025-01-09 at 20.44.21.png', 
    '/Users/<USER>/Desktop/Screenshot 2025-01-09 at 20.44.28.png',
    '/Users/<USER>/Desktop/Screenshot 2025-01-09 at 20.44.37.png',
    '/Users/<USER>/Desktop/Screenshot 2025-01-09 at 20.44.47.png'
]

merged_image = process_images(image_paths)
merged_image.save('/Users/<USER>/Desktop/merged_output.png')
