
"""
stage (stage_id, re-map case) -> randomly set various stages, but ensure each stage have records (more on closed stages)
rep (owner_user_id) -> 10 users randomly, but ensure each user have pipelines
created_at (created_at) -> randomly set for the past 12 months
amount -> randomly set between 100000 - 5000000
"""

import asyncio
import random
from datetime import timed<PERSON>ta
from decimal import Decimal
from uuid import UUID, uuid4

from salestech_be.core.user.service.user_service import (
    get_user_service_general,
)
from salestech_be.db.dao.pipeline_repository import PipelineRepository
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.contact_pipeline_association import (
    ContactPipelineAssociation,
)
from salestech_be.db.models.pipeline import Pipeline, PipelineStatus
from salestech_be.db.models.pipeline_stage_select_list_value_metadata import (
    PipelineOutcomeState,
)
from salestech_be.settings import settings
from salestech_be.util.time import zoned_utc_now

# ruff: noqa: S311


async def main() -> None:
    # list organizations or specify a org_id
    # check app_code of select list with this org
    # bootstrap select list by app_code
    db_engine = DatabaseEngine(url=str(settings.db_url))

    pipeline_repository = PipelineRepository(engine=db_engine)

    user_service = get_user_service_general(db_engine=db_engine)
    print(user_service)
    return
    # pipeline_service = get_pipeline_service(db_engine=db_engine)

    organization_id = UUID("490e7e45-ebe6-4ba5-8b12-ee18ab7c9872")
    default_account_id = UUID("********-cbe8-4c1e-a0ce-ec01dfac9280")
    default_contact_id = UUID("8469b81c-9527-4cf8-a698-fb82698ac506")
    num_of_pipeline_to_create = 40

    results = await user_service.user_repository.list_org_users_by_organization_id(
        organization_id=organization_id,
        active_users_only=True,
    )

    user_ids = {user.id for user, _ in results}
    print(f"Fetch {len(user_ids)} users from organization {organization_id}")

    pipeline_outcome_state = [
        None,
        PipelineOutcomeState.CLOSED_LOST,
        PipelineOutcomeState.CLOSED_WON,
    ]

    closed_won_stage_ids = [
        UUID("c700b95e-41bf-44c8-9344-99612fc6f61d"),
        UUID("410bb7fe-cd1a-4b91-8d10-ce2a52745400"),
        UUID("4ff1421b-d897-489a-9803-83adb44a5365"),
    ]

    closed_lost_stage_ids = [
        UUID("b74bd10a-3389-4164-9af0-104872dac453"),
        UUID("b8cef0a2-446e-4d1b-8a70-9725a9e6e57c"),
    ]

    deal_stage_ids = [
        UUID("a8633422-e8d6-4886-848a-8cdfc991944a"),
        UUID("5750e6be-92bd-4ad3-8ff1-8cf4ef2d2b20"),
        UUID("d1437ea8-0af4-4d2f-a983-33822cd94b16"),
        UUID("700e9bee-a71b-4187-8bda-95402aa207fe"),
        UUID("50b9eb4f-c48d-476e-8059-397e2ab18f62"),
    ]

    prospect_stage_ids = [
        UUID("5e2fe02e-e938-4670-b50b-858a8a0d97f4"),
        UUID("225c79a1-e051-4f1a-9c88-0f874d9561bd"),
        UUID("408422e8-677f-48d0-8afc-12804f0f2880"),
    ]

    closed_won_reason_select_list_value_ids = [
        UUID("2ca7f602-b052-42ca-8215-78ff268a2367"),
    ]

    closed_lost_reason_select_list_value_ids = [
        UUID("07c0985c-0d95-4cf9-b327-bfe2ced236ef"),
        UUID("34a34fa8-b93c-4f99-9194-4593018e11ad"),
        UUID("34ff9371-bae5-4258-9321-e68a48db69d5"),
        UUID("60f0025f-b1d9-435e-9245-f903b1fd69f2"),
        UUID("63e27b30-0d31-48d6-a31d-95c5b5b5fa62"),
        UUID("b38bea1f-9814-4d0a-8e11-b480295e2d1d"),
        UUID("e7f5eba1-73a6-41bf-8a92-20cf9ed6a94e"),
    ]

    for _ in range(num_of_pipeline_to_create):
        pipeline_id = uuid4()
        amount = random.randint(100000, 5000000)
        random_user_id = random.choice(list(user_ids))
        random_created_time = (
            zoned_utc_now()
            - timedelta(days=random.randint(10, 30))
            + timedelta(seconds=random.randint(100, 86300))
        )

        random_pipeline_outcome_state = random.choice(pipeline_outcome_state)
        if random_pipeline_outcome_state is None:
            random_state = random.choice([PipelineStatus.PROSPECT, PipelineStatus.DEAL])
            random_closed_time = None
            random_closed_reason_select_list_value_ids = None
            random_closed_user_id = None
            if random_state == PipelineStatus.PROSPECT:
                random_stage_id = random.choice(prospect_stage_ids)
            else:
                random_stage_id = random.choice(deal_stage_ids)

        elif random_pipeline_outcome_state == PipelineOutcomeState.CLOSED_LOST:
            random_state = PipelineStatus.DEAL
            random_stage_id = random.choice(closed_lost_stage_ids)
            random_closed_time = min(
                random_created_time
                + timedelta(days=random.randint(1, 10))
                + timedelta(seconds=random.randint(100, 86300)),
                zoned_utc_now(),
            )
            random_closed_user_id = random_user_id
            random_closed_reason_select_list_value_ids = random.sample(
                closed_lost_reason_select_list_value_ids,
                random.randint(1, len(closed_lost_reason_select_list_value_ids)),
            )
        elif random_pipeline_outcome_state == PipelineOutcomeState.CLOSED_WON:
            random_state = PipelineStatus.DEAL
            random_stage_id = random.choice(closed_won_stage_ids)
            random_closed_time = min(
                random_created_time
                + timedelta(days=random.randint(1, 10))
                + timedelta(seconds=random.randint(100, 86300)),
                zoned_utc_now(),
            )
            random_closed_user_id = random_user_id
            random_closed_reason_select_list_value_ids = random.sample(
                closed_won_reason_select_list_value_ids,
                random.randint(1, len(closed_won_reason_select_list_value_ids)),
            )

        pipeline_to_create = Pipeline(
            id=pipeline_id,
            organization_id=organization_id,
            account_id=default_account_id,
            display_name=f"Pipeline {random.randint(1, 1000000)}",
            state=random_state,
            status=random_state,
            stage_id=random_stage_id,
            amount=Decimal(amount),
            owner_user_id=random_user_id,
            created_by_user_id=random_user_id,
            updated_by_user_id=random_user_id,
            closed_by_user_id=random_closed_user_id,
            created_at=random_created_time,
            updated_at=random_created_time,
            closed_at=random_closed_time,
            closed_reason_select_list_value_ids=random_closed_reason_select_list_value_ids,
        )

        print(pipeline_to_create)

        await pipeline_repository.insert(pipeline_to_create)

        await pipeline_repository.insert(
            ContactPipelineAssociation(
                id=uuid4(),
                organization_id=organization_id,
                contact_id=default_contact_id,
                pipeline_id=pipeline_id,
                role=None,
                note=None,
                is_primary=True,
                created_by_user_id=random_user_id,
                updated_by_user_id=random_user_id,
                created_at=zoned_utc_now(),
                updated_at=zoned_utc_now(),
            )
        )


if __name__ == "__main__":
    asyncio.run(main())