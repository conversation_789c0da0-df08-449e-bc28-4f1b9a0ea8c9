import asyncio
import random

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from starlette.middleware.base import BaseHT<PERSON><PERSON>iddleware, RequestResponseEndpoint
from starlette.requests import Request
from starlette.responses import Response


class TimeoutMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: FastAPI, timeout: int):
        super().__init__(app)
        self.timeout = timeout

    async def dispatch(
        self, request: Request, call_next: RequestResponseEndpoint
    ) -> Response:
        try:
            # Use asyncio.wait_for to apply the timeout
            response = await asyncio.wait_for(call_next(request), timeout=self.timeout)
            return response
        except TimeoutError:
            # Raise an HTTPException if the request times out
            raise HTTPException(status_code=504, detail="Request timed out")
            return JSONResponse(
                    content={
                        "error_response_type": "GENERIC",
                        "error": "TimeoutError",
                        "message": "Request timed out",
                        "details": None,
                    },
                    status_code=504,
                )


app = FastAPI()

# Add the middleware with a 1-second timeout
app.add_middleware(
    TimeoutMiddleware,
    timeout=1
)


async def long_running_task():
    print("Starting long running task")
    await asyncio.sleep(10)  # sleep for 10 seconds
    print("Finished long running task")
    return random.random()  # return a random number


@app.get("/retrieve-data")
async def retrieve_data():
    # Simulate a long-running task
    result = await long_running_task()
    return {"message": result, "cache": False}


# Run FastAPI app
if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8002)