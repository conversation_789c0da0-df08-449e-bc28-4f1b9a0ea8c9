import asyncio
from uuid import UUID, uuid4

from salestech_be.core.crm_integrity.data_operation_tracker.atomic_operation_tracker_service import (
    AtomicOperationTrackerService,
)
from salestech_be.core.crm_integrity.data_operation_tracker.integrity_job_tracker_service import (
    IntegrityJobService,
)
from salestech_be.core.crm_integrity.types.activity_params import DataReplacementParam
from salestech_be.db.models.crm_integrity import (
    EntityType,
    IntegrityJobType,
    IntegrityOperation,
)
from salestech_be.task.instantiate.database import get_db_engine


async def person_research() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_person_research_activity import (
        ReplaceContactIdInPersonResearchActivity,
    )

    user_id = uuid4()
    organization_id = uuid4()
    contact_id = UUID("280c4db2-55eb-4cd4-b3d0-c6e82dc0f67d")
    new_contact_id = uuid4()

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInPersonResearchActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def activity_sub_reference() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_activity_sub_reference_activity import (
        ReplaceContactIdInActivitySubReferenceActivity,
    )

    # activity_id = ac916ca3-dd88-4cd7-8dc1-9041b51cfb81
    user_id = uuid4()
    organization_id = UUID("dc72691e-312e-493f-9ccc-c8f944a8c2d0")
    contact_id = UUID("d0ccc884-bfa1-4198-80b8-27ef51ef0ca0")
    new_contact_id = UUID("876ac616-4d56-4b2e-a614-76c6e605daee")
    # new_contact_id = UUID("5590630c-4532-4d57-8615-5321139ba232")
    # contact_id = UUID("b71ff577-c6c0-4677-811c-6c0fbc4d6dfc")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInActivitySubReferenceActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def pipeline_contact_association() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_contact_pipeline_association_activity import (
        ReplaceContactIdInContactPipelineAssociationsActivity,
    )

    user_id = uuid4()
    organization_id = UUID("b7b8bf65-1281-4ac6-94d4-46b9e8402925")
    # contact_id = UUID("4891cbcf-c4e3-4282-ad19-f64341907476")
    # new_contact_id = UUID("f024a299-5f05-4c37-9b1d-cfa24340f929")
    # new_contact_id = UUID("b1474ada-2518-404b-a54c-bb685f3e0efc")
    contact_id = UUID("f28f33e0-1169-4ce2-90c1-b8bc593dcb22")
    new_contact_id = UUID("d0bdcfdf-c562-4854-bd55-03cfd878d477")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInContactPipelineAssociationsActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def insights() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_insights_activity import (
        ReplaceContactIdInInsightsActivity,
    )

    user_id = uuid4()
    organization_id = UUID("69059acb-0ec3-48f4-a0d0-6f49765a2f69")
    contact_id = UUID("c8184f3f-a0b9-45fb-ad9d-62443da94e6a")
    new_contact_id = uuid4()

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInInsightsActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )


async def email_send_from() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_send_from_activity import (
        ReplaceContactIdInEmailsSendFromActivity,
    )

    user_id = uuid4()
    organization_id = UUID("1148558e-b267-4942-87f6-64bd39e479fe")
    new_contact_id = UUID("648f2e5e-4b98-4ee5-9d0d-c73535e7430b")
    contact_id = UUID("cfc9e781-ed62-47de-b541-712a7955eedc")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsSendFromActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_send_to() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_send_to_activity import (
        ReplaceContactIdInEmailsSendToActivity,
    )

    user_id = uuid4()
    organization_id = UUID("d6ecce56-d0fa-4f87-9265-20db7be9c5e2")
    new_contact_id = UUID("fc03f5b9-7901-428b-8812-b4750ca74cd3")
    contact_id = UUID("6aa505c9-edd5-4452-81a7-665b21744beb")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsSendToActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_send_from2() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_send_from_activity import (
        ReplaceContactIdInEmailsSendFromActivity,
    )

    user_id = uuid4()
    organization_id = UUID("1148558e-b267-4942-87f6-64bd39e479fe")
    contact_id = UUID("648f2e5e-4b98-4ee5-9d0d-c73535e7430b")
    new_contact_id = UUID("cfc9e781-ed62-47de-b541-712a7955eedc")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsSendFromActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_thread_participants() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_thread_participants_activity import (
        ReplaceContactIdInThreadParticipantsActivity,
    )

    user_id = uuid4()
    organization_id = UUID("47f8ffe4-1d51-4d85-9681-74d89fb6e6c6")
    contact_id = UUID("091133f2-1778-489c-8aa3-6b98f7d7e258")
    new_contact_id = UUID("5b97cbae-02f5-4d35-a36c-c5d706cc5f37")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInThreadParticipantsActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_cc() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_cc_activity import (
        ReplaceContactIdInEmailsCcActivity,
    )

    user_id = uuid4()
    organization_id = UUID("69059acb-0ec3-48f4-a0d0-6f49765a2f69")
    contact_id = UUID("f8aa6d04-f5a6-47a2-b758-2e8f85e0bdea")
    new_contact_id = UUID("003369b1-**************-6c4d07329af3")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsCcActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_bcc() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_bcc_activity import (
        ReplaceContactIdInEmailsBccActivity,
    )

    user_id = uuid4()
    organization_id = UUID("1148558e-b267-4942-87f6-64bd39e479fe")
    contact_id = UUID("b71ff577-c6c0-4677-811c-6c0fbc4d6dfc")
    new_contact_id = UUID("cfc9e781-ed62-47de-b541-712a7955eedc")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsBccActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )

async def email_reply_to() -> None:
    db_engine = await get_db_engine()
    from salestech_be.core.crm_integrity.associated_data_operation_activities.replace_contact_id_in_emails_reply_to_activity import (
        ReplaceContactIdInEmailsReplyToActivity,
    )

    user_id = uuid4()
    organization_id = UUID("ea2ff1be-9f44-481d-921c-48e7831019ea")
    contact_id = UUID("648f2e5e-4b98-4ee5-9d0d-c73535e7430b")
    new_contact_id = UUID("d3ac6ed1-f57f-45f4-945d-45f417ae72b3")

    integrity_job_tracker_service = IntegrityJobService(db_engine=db_engine)
    atomic_operation_tracker_service = AtomicOperationTrackerService(
        db_engine=db_engine
    )
    replace_activity = ReplaceContactIdInEmailsReplyToActivity(db_engine=db_engine)

    integrity_job = await integrity_job_tracker_service.create_integrity_job(
        user_id=user_id,
        organization_id=organization_id,
        job_type=IntegrityJobType.MERGE,
        src_entity_type=EntityType.CONTACT,
        src_entity_id=contact_id,
    )
    atomic_operation = (
        await atomic_operation_tracker_service.track_contact_account_association(
            integrity_job_id=integrity_job.id,
            contact_id=contact_id,
            account_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
    await replace_activity.replace_crm_id_in_relevant_entities(
        param=DataReplacementParam(
            replaced_entity_type=EntityType.CONTACT,
            integrity_job_id=integrity_job.id,
            integrity_operation_id=atomic_operation.id,
            primary_entity_id=contact_id,
            replaced_entity_id=new_contact_id,
            user_id=user_id,
            organization_id=organization_id,
        )
    )
if __name__ == "__main__":
    asyncio.run(email_bcc())
