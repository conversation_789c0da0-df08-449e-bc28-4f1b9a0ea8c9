import asyncio
from typing import Any
from uuid import UUID, uuid4

from salestech_be.core.workflow.types.schema import PrepareExecuteNodeParam
from salestech_be.db.models.workflow import (
    WorkflowBlockName,
    WorkflowNode,
    WorkflowNodePosition,
    WorkflowNodeType,
)
from salestech_be.task.instantiate.database import get_db_engine
from salestech_be.tests.integration.core.workflow.wokflow_execution_test_schemas import (
    mock_send_http_request_action_node_output_schema,
)
from salestech_be.util.time import zoned_utc_now


async def send_http_request() -> None:
    db_engine = await get_db_engine()

    send_http_request_action_node_id = uuid4()
    user_id = uuid4()
    organization_id = uuid4()

    mock_send_http_request_action_node_name = "send_http_request_action_10021"

    mock_send_http_request_action_node_input_schema: dict[str, Any] = {
        "data": [],
        "action": None,
        "logics": [],
        "conditions": [],
        "field_mappers": [
        {
            "source": {
                "type": "data",
                "value": "GET",
                "data_type": "string",
            },
            "target_attribute": "method",
        },
        {
            "source": {
                "type": "data",
                "value": "https://httpbin.org/json",
                "data_type": "string",
            },
            "target_attribute": "url",
        },
        {
            "source": {
                "type": "data",
                    "value": "test body",
                    "data_type": "string",
                },
                "target_attribute": "body",
            },
            {
                "source": {
                    "type": "data",
                    "value": "header_key_1",
                    "data_type": "string",
                },
                "target_attribute": "header__header_key_1",
            },
            {
                "source": {
                    "type": "data",
                    "value": "header_key_2",
                    "data_type": "string",
                },
                "target_attribute": "header__header_key_2",
            },
        ],
        "target_resource": None,
    }

    from salestech_be.core.workflow.activities.send_http_request_node_activity import (
        SendHttpRequestNodeActivity,
    )

    workflow_node = WorkflowNode(
        id=send_http_request_action_node_id,
        name=mock_send_http_request_action_node_name,
        type=WorkflowNodeType.ACTION,
        workflow_id=uuid4(),
        snapshot_id=uuid4(),
        block_id=UUID("db75eeb7-b8f1-48e1-b219-2837c0bae26d"),
        block_name=WorkflowBlockName.send_http_request_action,
        input_schema=mock_send_http_request_action_node_input_schema,
        output_schema=mock_send_http_request_action_node_output_schema,
        position=WorkflowNodePosition(x=0, y=0),
        owner_user_id=user_id,
        organization_id=organization_id,
        created_at=zoned_utc_now(),
        created_by_user_id=user_id,
    )

    result = await SendHttpRequestNodeActivity(db_engine=db_engine).run(
        param=PrepareExecuteNodeParam(
            node_to_execute=workflow_node,
            context={}
        )
    )
    print(result)


if __name__ == "__main__":
    asyncio.run(send_http_request())
