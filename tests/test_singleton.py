from typing import Any

from salestech_be.common.singleton import Singleton as Singleton2


kwd_mark = object()     # sentinel for separating cls and args and kwargs

class ParamedSingleton:
    def __new__(cls, *args: Any, **kwargs: Any) -> Any:
        # args and kwargs must be hashable
        key = (cls, kwd_mark, *args, kwd_mark, *tuple(sorted(kwargs.items())))
        hash_key = hash(key)
        if not hasattr(cls, "_instance_dict"):
            cls._instance_dict: dict[int, Any] = {}
        if hash_key not in cls._instance_dict:
            cls._instance_dict[hash_key] = super().__new__(cls)
        return cls._instance_dict[hash_key]



class Singleton:
    _instance: Any = None
    _initialized: bool = False

    def __new__(cls, *args: Any, **kwargs: Any) -> Any:
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        if not self._initialized:  # Prevent reinitialization
            self._initialized = True
            super().__init__(*args, **kwargs)


class A:
    def __init__(self, name: str):
        print("start __init__")
        self.name = name


class B(Singleton2, A):
    pass


if __name__ == "__main__":
    a1 = A("a1")
    a2 = A("a2")

    b1 = B("b1")
    b2 = B("b2")

    c1 = B("c")
    c2 = B("c")

    assert a1 != a2
    assert b1 == b2
    assert c1 == c2

    print(a1, a2, b1, b2, c1, c2, sep="\n")
    print(b1.name)
    print(b2.name)

    print(c1.name)
    print(c2.name)

