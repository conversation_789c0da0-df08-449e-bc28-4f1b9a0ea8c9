import asyncio
import json
from jose import jwt
from uuid import uuid4
from salestech_be.services.auth.types import ReevoJWTClaims
from salestech_be.web.api.analytics.service import generate_guest_token


async def test_guest_token():
    from salestech_be.settings import settings
    # prod
    settings.superset_guest_token_jwt_secret = "8EwaxFvMwiWN3NHYd9j0Lbq41zvM9HhAuflOvt93Pad5XFqhBuauP7mP"
    settings.superset_dashboard_id = "b373a363-7588-4835-bcc8-1c977d71c98e"
    settings.superset_guest_token_jwt_audience = "https://analytics.reevo.ai"
    settings.superset_guest_token_jwt_ttl = 86400 * 365
    claims = ReevoJWTClaims(
        sub="3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa",
        org="cce7b290-8a08-4904-a6c7-2b6613877cf5",
    )
    claims = ReevoJWTClaims(
        sub="36ad3654-0978-41f6-b25d-51915d47a401",
        org="c6734584-b3f3-4608-b62e-a993f4703a8e",
    )
    claims = ReevoJWTClaims(
        sub="2d435a27-6d2e-4e1c-a47b-da37bfa02fb6",
        org="b3c5bc5d-1eae-4586-b9bd-db8486e6b689",
    )
    claims = ReevoJWTClaims(
        sub="403dbc68-7d8e-471f-8429-30211fd2124c",
        org="7f81076d-9cef-4c0e-93d9-de940c5a5a55",
    )
    claims = ReevoJWTClaims(
        sub="d0024105-d5d6-44bd-8d74-ca048eca7744",
        org="4cfb4ddd-61b1-4bcf-810e-17f5fa6c4294",
    )
    claims = ReevoJWTClaims(
        sub="528c330f-45c9-4fbe-94b1-a4613699d888",
        org="aebfaa86-bdff-4538-8817-19abe2918b98",
    )
    claims = ReevoJWTClaims(
        sub="9ab23c4a-63a4-477d-b148-11beb687bca4",
        org="4d29f892-7e25-4efa-ad0b-f348bd0fc0fc",
    )
    claims = ReevoJWTClaims(
        sub="ab6a2bfd-f774-4f3e-bc16-e6b07fa6cd47",
        org="4d29f892-7e25-4efa-ad0b-f348bd0fc0fc",
    )
    # print(claims)
    # quit()
    token = await generate_guest_token(claims)
    token = token.token
    print(token)
    # return

    data = jwt.decode(  # type: ignore[no-any-return]
        token,
        key=settings.superset_guest_token_jwt_secret,
        algorithms=[settings.superset_guest_token_jwt_algorithm],
        options={
            "verify_signature": False,
            "verify_aud": False,
            "verify_iat": False,
            "verify_exp": False,
            "verify_nbf": False,
            "verify_iss": False,
            "verify_sub": False,
            "verify_jti": False,
            "verify_at_hash": False,
            "require_aud": False,
            "require_iat": False,
            "require_exp": False,
            "require_nbf": False,
            "require_iss": False,
            "require_sub": False,
            "require_jti": False,
            "require_at_hash": False,
            "leeway": 0,
        },
    )
    print(json.dumps(data, indent=2))


if __name__ == "__main__":
    asyncio.run(test_guest_token())
