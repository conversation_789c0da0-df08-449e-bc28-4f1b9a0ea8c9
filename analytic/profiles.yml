reevo_analytic:
  target: dev
  outputs:
    dev:
      type: duckdb
      path: 'reevo_analytic.duckdb'

materialize:
  target: dev
  outputs:
    dev:
      type: materialize
      host: localhost
      port: 6875
      user: materialize
      pass: materialize
      dbname: materialize
      cluster: quickstart
      schema: public
      keepalives_idle: 0 # default: 0, indicating the system default
      connect_timeout: 10 # default: 10 seconds
      retries: 1 # default: 1, retry on error/timeout when opening connections

