semantic_models:
  - name: contacts
    description: All contacts.
    defaults:
      agg_time_dimension: ds
    model: ref('contacts')
    entities:
      - name: contact
        type: primary
        expr: id
      - name: account
        type: foreign
        expr: primary_account_id
      - name: stage
        type: foreign
        expr: stage_id
    measures:
      - name: count_contacts
        expr: "1"
        agg: SUM
    dimensions:
      - name: ds
        type: time
        type_params:
          time_granularity: day
      - name: primary_account_id
        type: categorical
      - name: stage_id
        type: categorical
