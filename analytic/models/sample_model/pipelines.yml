semantic_models:
  - name: pipelines
    description: |
      Each row represents one pipeline.
    defaults:
      agg_time_dimension: ds
    model: ref('pipelines')
    entities:
      - name: pipeline
        type: primary
        expr: id
      - name: account
        type: foreign
        expr: account_id
      - name: contact
        type: foreign
        expr: primary_contact_id
    measures:
      - name: pipelines
        description: The total number of pipelines
        expr: "1"
        agg: SUM
    dimensions:
      - name: ds
        type: time
        type_params:
          time_granularity: day
