{{
    config(
        materialized = 'table',
    )
}}

with days as (
    -- Only generate dates for 2022 since that's what's in the seed data.
    -- Note that `date_spine` does not include the end date.
    {{
        dbt.date_spine(
            'day',
            "DATE '2022-01-01'",
            "DATE '2042-01-01'"
        )
    }}

),

final as (
    select cast(date_day as date) as date_day
    from days
)


select * from final
where date_day >= DATE '2022-01-01'
and date_day  < DATE '2042-01-01'
