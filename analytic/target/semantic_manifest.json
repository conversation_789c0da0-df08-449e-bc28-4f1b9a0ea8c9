{"semantic_models": [{"name": "contacts", "defaults": {"agg_time_dimension": "ds"}, "description": "All contacts.", "node_relation": {"alias": "contacts", "schema_name": "main", "database": "reevo_analytic", "relation_name": "\"reevo_analytic\".\"main\".\"contacts\""}, "primary_entity": null, "entities": [{"name": "contact", "description": null, "type": "primary", "role": null, "expr": "id", "metadata": null, "label": null}, {"name": "account", "description": null, "type": "foreign", "role": null, "expr": "primary_account_id", "metadata": null, "label": null}, {"name": "stage", "description": null, "type": "foreign", "role": null, "expr": "stage_id", "metadata": null, "label": null}], "measures": [{"name": "count_contacts", "agg": "sum", "description": null, "create_metric": false, "expr": "1", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}], "dimensions": [{"name": "ds", "description": null, "type": "time", "is_partition": false, "type_params": {"time_granularity": "day", "validity_params": null}, "expr": null, "metadata": null, "label": null}, {"name": "primary_account_id", "description": null, "type": "categorical", "is_partition": false, "type_params": null, "expr": null, "metadata": null, "label": null}, {"name": "stage_id", "description": null, "type": "categorical", "is_partition": false, "type_params": null, "expr": null, "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}, {"name": "accounts", "defaults": null, "description": "All Accounts", "node_relation": {"alias": "accounts", "schema_name": "main", "database": "reevo_analytic", "relation_name": "\"reevo_analytic\".\"main\".\"accounts\""}, "primary_entity": null, "entities": [{"name": "account", "description": null, "type": "primary", "role": null, "expr": "id", "metadata": null, "label": null}], "measures": [], "dimensions": [{"name": "status", "description": "The status of the account.", "type": "categorical", "is_partition": false, "type_params": null, "expr": null, "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}, {"name": "pipelines", "defaults": {"agg_time_dimension": "ds"}, "description": "Each row represents one pipeline.\n", "node_relation": {"alias": "pipelines", "schema_name": "main", "database": "reevo_analytic", "relation_name": "\"reevo_analytic\".\"main\".\"pipelines\""}, "primary_entity": null, "entities": [{"name": "pipeline", "description": null, "type": "primary", "role": null, "expr": "id", "metadata": null, "label": null}, {"name": "account", "description": null, "type": "foreign", "role": null, "expr": "account_id", "metadata": null, "label": null}, {"name": "contact", "description": null, "type": "foreign", "role": null, "expr": "primary_contact_id", "metadata": null, "label": null}], "measures": [{"name": "pipelines", "agg": "sum", "description": "The total number of pipelines", "create_metric": false, "expr": "1", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}], "dimensions": [{"name": "ds", "description": null, "type": "time", "is_partition": false, "type_params": {"time_granularity": "day", "validity_params": null}, "expr": null, "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}], "metrics": [{"name": "pipelines", "description": "", "type": "simple", "type_params": {"measure": {"name": "pipelines", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "pipelines", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Pipelines", "config": {"meta": {}}, "time_granularity": null}], "project_configuration": {"time_spine_table_configurations": [], "metadata": null, "dsi_package_version": {"major_version": "0", "minor_version": "7", "patch_version": "4"}, "time_spines": [{"node_relation": {"alias": "all_days", "schema_name": "main", "database": "reevo_analytic", "relation_name": "\"reevo_analytic\".\"main\".\"all_days\""}, "primary_column": {"name": "date_day", "time_granularity": "day"}, "custom_granularities": []}]}, "saved_queries": []}