{"_invocation_id": "3b112f00-87ee-49b7-9376-cc960b1025ec", "linked": {"0": {"name": "source.reevo_analytic.sample_seed.accounts_seed", "type": "source", "succ": [4]}, "1": {"name": "source.reevo_analytic.sample_seed.contacts_seed", "type": "source", "succ": [5]}, "2": {"name": "source.reevo_analytic.sample_seed.pipelines_seed", "type": "source", "succ": [6]}, "3": {"name": "model.reevo_analytic.all_days", "type": "model"}, "4": {"name": "model.reevo_analytic.accounts", "type": "model", "succ": [12]}, "5": {"name": "model.reevo_analytic.contacts", "type": "model", "succ": [10]}, "6": {"name": "model.reevo_analytic.pipelines", "type": "model", "succ": [11]}, "7": {"name": "seed.reevo_analytic.accounts_seed", "type": "seed"}, "8": {"name": "seed.reevo_analytic.pipelines_seed", "type": "seed"}, "9": {"name": "seed.reevo_analytic.contacts_seed", "type": "seed"}, "10": {"name": "semantic_model.reevo_analytic.contacts", "type": "semantic_model"}, "11": {"name": "semantic_model.reevo_analytic.pipelines", "type": "semantic_model", "succ": [13]}, "12": {"name": "semantic_model.reevo_analytic.accounts", "type": "semantic_model"}, "13": {"name": "metric.reevo_analytic.pipelines", "type": "metric"}}, "with_test_edges": {"0": {"name": "source.reevo_analytic.sample_seed.accounts_seed", "type": "source", "succ": [4]}, "1": {"name": "source.reevo_analytic.sample_seed.contacts_seed", "type": "source", "succ": [5]}, "2": {"name": "source.reevo_analytic.sample_seed.pipelines_seed", "type": "source", "succ": [6]}, "3": {"name": "model.reevo_analytic.all_days", "type": "model"}, "4": {"name": "model.reevo_analytic.accounts", "type": "model", "succ": [12]}, "5": {"name": "model.reevo_analytic.contacts", "type": "model", "succ": [10]}, "6": {"name": "model.reevo_analytic.pipelines", "type": "model", "succ": [11]}, "7": {"name": "seed.reevo_analytic.accounts_seed", "type": "seed"}, "8": {"name": "seed.reevo_analytic.pipelines_seed", "type": "seed"}, "9": {"name": "seed.reevo_analytic.contacts_seed", "type": "seed"}, "10": {"name": "semantic_model.reevo_analytic.contacts", "type": "semantic_model"}, "11": {"name": "semantic_model.reevo_analytic.pipelines", "type": "semantic_model", "succ": [13]}, "12": {"name": "semantic_model.reevo_analytic.accounts", "type": "semantic_model"}, "13": {"name": "metric.reevo_analytic.pipelines", "type": "metric"}}}