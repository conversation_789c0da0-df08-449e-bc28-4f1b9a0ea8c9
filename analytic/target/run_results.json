{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.2", "generated_at": "2025-02-27T16:02:58.633058Z", "invocation_id": "3b112f00-87ee-49b7-9376-cc960b1025ec", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.473062Z", "completed_at": "2025-02-27T16:02:58.477396Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.477572Z", "completed_at": "2025-02-27T16:02:58.511001Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.*****************, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.reevo_analytic.accounts", "compiled": true, "compiled_code": "select\n    *\nfrom \"reevo_analytic\".\"main\".\"accounts_seed\"", "relation_name": "\"reevo_analytic\".\"main\".\"accounts\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.513512Z", "completed_at": "2025-02-27T16:02:58.530876Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.531066Z", "completed_at": "2025-02-27T16:02:58.557655Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.044722795486450195, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.reevo_analytic.all_days", "compiled": true, "compiled_code": "\n\nwith days as (\n    -- Only generate dates for 2022 since that's what's in the seed data.\n    -- Note that `date_spine` does not include the end date.\n    \n\n\n    \n\n\n    with rawdata as (\n\n        \n\n    \n\n    with p as (\n        select 0 as generated_number union all select 1\n    ), unioned as (\n\n    select\n\n    \n    p0.generated_number * power(2, 0)\n     + \n    \n    p1.generated_number * power(2, 1)\n     + \n    \n    p2.generated_number * power(2, 2)\n     + \n    \n    p3.generated_number * power(2, 3)\n     + \n    \n    p4.generated_number * power(2, 4)\n     + \n    \n    p5.generated_number * power(2, 5)\n     + \n    \n    p6.generated_number * power(2, 6)\n     + \n    \n    p7.generated_number * power(2, 7)\n     + \n    \n    p8.generated_number * power(2, 8)\n     + \n    \n    p9.generated_number * power(2, 9)\n     + \n    \n    p10.generated_number * power(2, 10)\n     + \n    \n    p11.generated_number * power(2, 11)\n     + \n    \n    p12.generated_number * power(2, 12)\n    \n    \n    + 1\n    as generated_number\n\n    from\n\n    \n    p as p0\n     cross join \n    \n    p as p1\n     cross join \n    \n    p as p2\n     cross join \n    \n    p as p3\n     cross join \n    \n    p as p4\n     cross join \n    \n    p as p5\n     cross join \n    \n    p as p6\n     cross join \n    \n    p as p7\n     cross join \n    \n    p as p8\n     cross join \n    \n    p as p9\n     cross join \n    \n    p as p10\n     cross join \n    \n    p as p11\n     cross join \n    \n    p as p12\n    \n    \n\n    )\n\n    select *\n    from unioned\n    where generated_number <= 7305\n    order by generated_number\n\n\n\n    ),\n\n    all_periods as (\n\n        select (\n            \n\n    date_add(DATE '2022-01-01', interval (row_number() over (order by 1) - 1) day)\n\n\n        ) as date_day\n        from rawdata\n\n    ),\n\n    filtered as (\n\n        select *\n        from all_periods\n        where date_day <= DATE '2042-01-01'\n\n    )\n\n    select * from filtered\n\n\n\n),\n\nfinal as (\n    select cast(date_day as date) as date_day\n    from days\n)\n\n\nselect * from final\nwhere date_day >= DATE '2022-01-01'\nand date_day  < DATE '2042-01-01'", "relation_name": "\"reevo_analytic\".\"main\".\"all_days\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.559544Z", "completed_at": "2025-02-27T16:02:58.561094Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.561262Z", "completed_at": "2025-02-27T16:02:58.569154Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.010230064392089844, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.reevo_analytic.contacts", "compiled": true, "compiled_code": "select\n    *\nfrom \"reevo_analytic\".\"main\".\"contacts_seed\"", "relation_name": "\"reevo_analytic\".\"main\".\"contacts\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.570834Z", "completed_at": "2025-02-27T16:02:58.572298Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.572463Z", "completed_at": "2025-02-27T16:02:58.580660Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.010392189025878906, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.reevo_analytic.pipelines", "compiled": true, "compiled_code": "select\n    *\nfrom \"reevo_analytic\".\"main\".\"pipelines_seed\"", "relation_name": "\"reevo_analytic\".\"main\".\"pipelines\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.582474Z", "completed_at": "2025-02-27T16:02:58.582476Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.582631Z", "completed_at": "2025-02-27T16:02:58.611716Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.029857873916625977, "adapter_response": {"_message": "INSERT 1", "code": "INSERT", "rows_affected": 1}, "message": "INSERT 1", "failures": null, "unique_id": "seed.reevo_analytic.accounts_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.613390Z", "completed_at": "2025-02-27T16:02:58.613392Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.613529Z", "completed_at": "2025-02-27T16:02:58.620875Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.008018970489501953, "adapter_response": {"_message": "INSERT 2", "code": "INSERT", "rows_affected": 2}, "message": "INSERT 2", "failures": null, "unique_id": "seed.reevo_analytic.contacts_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-27T16:02:58.622517Z", "completed_at": "2025-02-27T16:02:58.622519Z"}, {"name": "execute", "started_at": "2025-02-27T16:02:58.622683Z", "completed_at": "2025-02-27T16:02:58.629824Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.007869243621826172, "adapter_response": {"_message": "INSERT 4", "code": "INSERT", "rows_affected": 4}, "message": "INSERT 4", "failures": null, "unique_id": "seed.reevo_analytic.pipelines_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}], "elapsed_time": 0.2186441421508789, "args": {"which": "build", "exclude": [], "send_anonymous_usage_stats": true, "show_resource_report": false, "warn_error_options": {"include": [], "exclude": []}, "include_saved_query": false, "resource_types": [], "introspect": true, "show": false, "indirect_selection": "eager", "static_parser": true, "log_level": "info", "partial_parse_file_diff": true, "log_file_max_bytes": 10485760, "print": true, "require_nested_cumulative_type_params": false, "require_resource_names_without_spaces": false, "defer": false, "use_colors": true, "empty": false, "write_json": true, "export_saved_queries": false, "source_freshness_run_project_hooks": false, "exclude_resource_types": [], "partial_parse": true, "strict_mode": false, "cache_selected_only": false, "favor_state": false, "printer_width": 80, "vars": {}, "version_check": true, "require_explicit_package_overrides_for_builtin_materializations": true, "log_format": "default", "log_format_file": "debug", "use_colors_file": true, "macro_debugging": false, "invocation_command": "dbt build", "log_level_file": "debug", "state_modified_compare_vars": false, "populate_cache": true, "log_path": "/Users/<USER>/git/reevo-ng/analytic/logs", "skip_nodes_if_on_run_start_fails": false, "state_modified_compare_more_unrendered_values": false, "require_yaml_configuration_for_mf_time_spines": false, "project_dir": "/Users/<USER>/git/reevo-ng/analytic", "profiles_dir": "/Users/<USER>/git/reevo-ng/analytic", "select": [], "quiet": false, "require_batched_execution_for_custom_microbatch_strategy": false}}