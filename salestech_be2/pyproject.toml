[tool.poetry]
name = "salestech_be"
version = "0.1.0"
description = ""
authors = []
maintainers = []
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "^0.110.2"
uvicorn = { version = "^0.26.0", extras = ["standard"] }
gunicorn = "^22.0.0"
pydantic = "2.8.2"
pydantic-settings = "^2"
yarl = "^1.9.2"
ujson = "^5.8.0"
SQLAlchemy = { version = "^2.0.25", extras = ["asyncio"] }
alembic = "^1.11.1"
asyncpg = { version = "^0.29.0", extras = ["sa"] }
redis = { version = "^4.6.0", extras = ["hiredis"] }
httptools = "^0.6.0"
prometheus-client = "^0.17.0"
prometheus-fastapi-instrumentator = "6.0.0"
sentry-sdk = "^2.9.0"
sendgrid = "^6.11.0"
opentelemetry-api = "^1.27.0"
opentelemetry-sdk = "^1.27.0"
opentelemetry-exporter-otlp = "^1.27.0"
opentelemetry-instrumentation = "^0.48b0"
opentelemetry-instrumentation-fastapi = "^0.48b0"
opentelemetry-instrumentation-redis = "^0.48b0"
opentelemetry-instrumentation-sqlalchemy = "^0.48b0"
loguru = "^0.7.0"
aiokafka = "^0.10.0"
pytz = "^2024.1"
types-pytz = "^2023.3.1.1"
pendulum = "^3.0.0"
httpx = "0.27.0"
itsdangerous = "^2.1.2"
authlib = "^1.3.0"
fastapi-users = { extras = ["oauth", "sqlalchemy"], version = "^12.1.3" }
anyio = "^4.3.0"
nylas = "^6.0.1"
email-validator = "^2.1.0.post1"
orjson = "^3.9.15"
langchain = "^0.2.6"
langchain-openai = "^0.1.10"
types-requests = "^2.31.0.20240218"
requests = "^2.31.0"
gevent = "^24.2.1"
botocore = "^1.34.74"
boto3 = "^1.34.74"
zeep = "^4.2.1"
async-lru = "^2.0.4"
contextvars = "^2.4"
APScheduler = "^3.10.4"
dramatiq = { extras = ["redis", "watch"], version = "^1.16.0" }
croniter = "^2.0.3"
types-croniter = "^2.0.0.20240321"
langchain-aws = "^0.1.0"
pydantic-xml = "^2.9.2"
json-repair = "^0"
python-dateutil = "^2.9.0.post0"
types-python-dateutil = "^2.9.0.20240316"
auth0-python = "^4.7.1"
uvloop = "^0.19.0"
sentry-dramatiq = "^0.3.3"
datadog = "^0.49.1"
typing-extensions = ">=4.11.0"
google-auth-httplib2 = "^0.2.0"
google-api-python-client = "^2.127.0"
google-auth-oauthlib = "^1.2.0"
google-apps-meet = "^0.1.6"
Jinja2 = "^3.1.4"
python-jose = { extras = ["pycryptodome"], version = "^3.3.0" }
twilio = "^9.0.5"
phonenumbers = "^8.13.37"
frozendict = "^2.4.4"
tldextract = "^5.1.2"
pytest-split = "^0.8.2"
pytest-ordering = "^0.6"
langchain-anthropic = "^0.1.15"
transformers = "^4.41.2"
types-deprecated = "^1.2.9.20240311"
langchain-community = "^0.2.6"
svix = "^1.24.0"
wiremock = "^2.6.1"
testcontainers = "^4.7.1"
validators = "^0.33.0"
pyinstrument = "^4.6.2"
icalendar = "^5.0.13"
monday = "^1.3.3"
langgraph = "^0.2.3"
langchain-google-community = "^1.0.7"
playwright = "^1.46.0"
html2text = "^2024.2.26"
networkx = "^3.3"
beautifulsoup4 = "^4.12.3"
temporalio = "^1.7.0"
langfuse = "^2.46.3"
litellm = "^1.47.2"
llama-index = "^0.11.8"
langsmith = "^0.1.117"
lz4 = "^4.3.3"
voyageai = "^0.2.3"
cron-validator = "^1.0.8"
elasticsearch = {version = "^8.15.1", extras = ["async"]}
fire = "^0.7.0"
pony = "^0.7.19"
tortoise-orm = "^0.21.7"
django = "^5.1.3"

[tool.poetry.dev-dependencies]
pytest = "^7.2.1"
flake8 = "^7.0.0"
mypy = ">=1.10.0"
isort = "^5.11.4"
pre-commit = "^3.0.1"
black = "^22.12.0"
autoflake = "^1.6.1"
pytest-cov = "^4.0.0"
pytest-env = "^0.8.1"
fakeredis = "^2.5.0"
ipython = "^8.20.0"
pytest-asyncio = "^0.23.7"

[tool.poetry.scripts]
touch_latest_alembic_revision = "salestech_be.db.migrations.hook:touch_latest_revision"

[tool.poetry.group.dev.dependencies]
icecream = "^2.1.3"
ruff = ">=0.5.6"
faker-enum = "^0.0.2"
polyfactory = "^2.15.0"
pytest-mock = "^3.14.0"
fastapi-cli = "^0.0.3"
jupyter = "^1.0.0"
debugpy = "^1.8.5"

[tool.isort]
profile = "black"
multi_line_output = 3
src_paths = ["salestech_be", "salestech_tasks"]

[tool.mypy]
strict = true
ignore_missing_imports = true

# Dynamic typing constraints
disallow_subclassing_any = true
disallow_untyped_calls = true
disallow_untyped_decorators = true
disallow_untyped_defs = true
check_untyped_defs = true
untyped_calls_exclude = ["loguru._logger", "dramatiq", "zeep"]
disallow_any_generics = true
#disallow_any_decorated = true
#disallow_any_expr = true
#disallow_any_explicit = true

# None and Optional handling
strict_optional = true

# Warnings
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_return_any = true
warn_unused_configs = true

pretty = true
show_error_codes = true
show_error_context = true
implicit_reexport = true
namespace_packages = true
plugins = ["pydantic.mypy"]
python_version = "3.12"

# Remove this and add `types-redis`
# when the issue https://github.com/python/typeshed/issues/8242 is resolved.
[[tool.mypy.overrides]]
module = ['redis.asyncio']
ignore_missing_imports = true

[tool.pydantic-mypy]
init_forbid_extra = true
init_typed = true
warn_required_dynamic_aliases = true


[tool.pytest.ini_options]
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore:.*unclosed.*:ResourceWarning",
    "ignore:.*MemcachedBackend.*:ImportWarning",
    "ignore:.*Valid config keys have changed in V2.*:UserWarning",
    "ignore:.*pandas.*:ImportWarning",
    "ignore:.*scipy.*:ImportWarning"
]
env = [
    "SALESTECH_BE_ENVIRONMENT=pytest",
    "SALESTECH_BE_DB_BASE=salestech_be",
    "SALESTECH_BE_SENTRY_DSN=",
    "USE_STUB_BROKER=1",
]


[fastapi-template.options]
project_name = "salestech_be"
api_type = "rest"
enable_redis = "True"
enable_rmq = "None"
ci_type = "github"
enable_migrations = "True"
enable_taskiq = "None"
enable_kube = "None"
kube_name = "salestech-be"
enable_routers = "True"
enable_kafka = "True"
enable_loguru = "True"
traefik_labels = "True"
add_dummy = "True"
orm = "sqlalchemy"
self_hosted_swagger = "None"
prometheus_enabled = "True"
sentry_enabled = "True"
otlp_enabled = "True"
pydanticv1 = "None"
gunicorn = "True"
add_users = "None"
cookie_auth = "None"
jwt_auth = "None"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
