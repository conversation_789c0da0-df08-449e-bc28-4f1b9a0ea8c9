import asyncio
import datetime
import uuid
import fire
from uuid import UUID

from salestech_be.core.crm_sync.hubspot.hubspot_service import \
    get_hubspot_service_by_db_engine
from salestech_be.task.task_definition.form import send_scheduled_form_submissions_sync_tasks
from salestech_be.task.instantiate.database import get_db_engine

workflow_id = UUID("a84939cf-01fd-4adb-a5ea-0dabd3c91ed7")
user_id = uuid.UUID("36ad3654-0978-41f6-b25d-51915d47a401")
orgnization_id = uuid.UUID("c6734584-b3f3-4608-b62e-a993f4703a8e")


async def hubspot():
    db_engine = await get_db_engine()
    hubspot_service = get_hubspot_service_by_db_engine(db_engine=db_engine)
    # print(await hubspot_service.get_hubspot_integration(organization_id=orgnization_id))
    # print(await hubspot_service.get_or_refresh_hubspot_access_token(organization_id=orgnization_id))
    # print(await hubspot_service.sync_hubspot_forms(organization_id=orgnization_id))
    # await send_scheduled_form_submissions_sync_tasks()
    await hubspot_service.sync_hubspot_form_submissions(
        form_id=UUID("e85d5837-b641-4151-9fed-9d6d7f24d938"), organization_id=orgnization_id
    )
    # print(await hubspot_service.form_repository.find_by_ids([UUID("e85d5837-b641-4151-9fed-9d6d7f24d938")]))


async def contact():
    db_engine = await get_db_engine()
    from salestech_be.core.contact.service.contact_service import get_contact_service
    from salestech_be.core.lead.service.lead_service import get_lead_service
    lead_service = get_lead_service(db_engine=db_engine)
    contact_service = get_contact_service(db_engine=db_engine, lead_service=lead_service)
    print(await contact_service.enrich_contact_by_ids_v2(
        contact_ids=[UUID("0001d2f2-27b1-4360-b1f2-e2f314f3b5de")],
        user_id=UUID("7d1f713b-d2ec-4e27-8172-b4376d164f9a"),
        organization_id=UUID("ae538ec2-7ccd-4870-8d07-1dc21c6bb884"),
        enrich_phone_numbers=False,
    ))


async def get_trigger_node():
    db_engine = await get_db_engine()
    from salestech_be.db.dao.workflow_repository import WorkflowNodeRepository
    workflow_node_repo = WorkflowNodeRepository(engine=db_engine)
    node = await workflow_node_repo.find_trigger_node_by_workflow_id(
        workflow_id=workflow_id, organization_id=orgnization_id
    )
    print(node)


async def create_schedule():
    from salestech_be.util.time import zoned_utc_now
    # print(datetime.datetime.now())
    # print(zoned_utc_now())
    # print(zoned_utc_now().tzinfo)
    # print(datetime.datetime.now().astimezone())
    # print(datetime.datetime.now().astimezone().tzname())
    # print(datetime.datetime.now().astimezone().tzinfo)
    # quit()

    from salestech_be.temporal.client import get_client
    from salestech_be.core.workflow.service.workflow_temporal_service import WorkflowTemporalService
    client = await get_client()
    db_engine = await get_db_engine()
    workflow_temporal_service = WorkflowTemporalService(db_engine=db_engine, client=client)
    print(workflow_temporal_service.client.get_schedule_handle("xxx"))


async def validate_input_schema():
    from salestech_be.util.time import zoned_utc_now
    from salestech_be.util.pydantic_types.time import ZoneRequiredDateTime
    from salestech_be.util.time import convert_utc_to_local
    utc_now = zoned_utc_now()
    sh_now = convert_utc_to_local(utc_datetime=utc_now, local_timezone="Asia/Shanghai")
    paris_now = convert_utc_to_local(utc_datetime=utc_now, local_timezone="Europe/Paris")

    from salestech_be.db.dao.workflow_repository import WorkflowNodeRepository
    from salestech_be.core.workflow.types.schema import WorkflowNodeInputSchema
    from salestech_be.db.models.workflow import WorkflowNode
    db_engine = await get_db_engine()
    workflow_node_repo = WorkflowNodeRepository(engine=db_engine)
    node = await workflow_node_repo.find_by_primary_key_or_fail(
        WorkflowNode,
        id=UUID("64e086d3-fee2-42b5-a3c9-86670aab1390"),
        exclude_deleted_or_archived=False,
    )
    print(node)
    node_input = WorkflowNodeInputSchema.model_validate(node.input_schema)


if __name__ == "__main__":
    fire.Fire()
