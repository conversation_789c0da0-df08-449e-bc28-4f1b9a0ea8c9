
generated from claude 3.5 based on

[[For LLM context] attio workflow API spec context](https://www.notion.so/For-LLM-context-attio-workflow-API-spec-context-dae1fbb97094469a98f713a3115157c7?pvs=21)

[[attempt 1] AI generated Attio workflow API spec ](https://www.notion.so/attempt-1-AI-generated-Attio-workflow-API-spec-f60f691c3a3349fd89420c65c1a0690e?pvs=21)

# Attio API Specification

## Summary of Resources

1. **Workflows**: The core automation building blocks. Used to create automated processes triggered by events or schedules.
Example: A workflow that automatically assigns new leads to sales reps based on territory.
UI: Built and managed in the visual workflow builder.
2. **Workflow Snapshots**: Versions or states of a workflow.
Example: Capturing the state of a workflow before and after major changes.
UI: Accessible in the workflow history view, allowing rollbacks if needed.
3. **Workflow Blocks**: Reusable components for building workflows.
Example: A "Send HTTP Request" block that can be used in multiple workflows.
UI: Available in the block library when building workflows.
4. **Workflow Nodes**: Individual steps or actions within a workflow.
Example: A node that sends an email, updates a record, or performs a calculation.
UI: Represented as blocks on the workflow canvas that can be connected and configured.
5. **Workflow Edges**: Connections between nodes in a workflow.
Example: The path between an "If/Else" node and its subsequent actions.
UI: Visualized as lines connecting blocks on the workflow canvas.
6. **Workflow Runs**: Executions of a workflow.
Example: A specific instance of a lead assignment workflow running for a new lead.
UI: Viewable in the run viewer, showing execution details and any errors.
7. **Workflow Templates**: Pre-defined workflow structures.
Example: A template for customer onboarding that can be customized.
UI: Available when creating new workflows, providing quick-start options.
8. **Workflow Memberships**: Access control for workflows.
Example: Granting specific team members edit access to a critical workflow.
UI: Managed through the workflow settings panel.
9. **Workflow Notes**: Annotations or comments on workflows.
Example: Documentation explaining complex logic within a workflow.
UI: Added directly on the workflow canvas for context.

Additional components:

1. **Triggers**: Events that start a workflow.
Example: A new record being created or a form submission.
UI: The first block in a workflow, configurable with various options.
2. **Actions**: Operations performed by workflow nodes.
Example: Updating a record, sending an email, or making an API call.
UI: Configurable blocks added to the workflow canvas.
3. **Conditions**: Logic gates within workflows.
Example: An "If/Else" block that routes the workflow based on criteria.
UI: Special blocks that create branching paths in the workflow.

## API Endpoints

## Workflows

### Get Workflow List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "title": "string",
      "description": "string",
      "ai_title": "string",
      "ai_description": "string",
      "status": "string",
      "first_set_live_at": "string",
      "last_status_change": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string",
      "is_archived": boolean,
      "is_transient": boolean,
      "dismissed_cycle_warning": boolean,
      "max_credits_per_run": number
    }
  ]
}

```

### Get Workflow

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "title": "string",
  "description": "string",
  "ai_title": "string",
  "ai_description": "string",
  "status": "string",
  "first_set_live_at": "string",
  "last_status_change": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "is_archived": boolean,
  "is_transient": boolean,
  "dismissed_cycle_warning": boolean,
  "max_credits_per_run": number
}

```

### Create Workflow

```markdown
PUT /api/common/workspaces/{workspace_id}/workflows

Request:
{
  "title": "string",
  "description": "string",
  "workspace_role": "string"
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "title": "string",
  "description": "string",
  "ai_title": "string",
  "ai_description": "string",
  "status": "string",
  "first_set_live_at": "string",
  "last_status_change": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "is_archived": boolean,
  "is_transient": boolean,
  "dismissed_cycle_warning": boolean,
  "max_credits_per_run": number
}

```

### Update Workflow

```markdown
PATCH /api/common/workspaces/{workspace_id}/workflows/{workflow_id}

Request:
{
  "title": "string",
  "description": "string"
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "title": "string",
  "description": "string",
  "ai_title": "string",
  "ai_description": "string",
  "status": "string",
  "first_set_live_at": "string",
  "last_status_change": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "is_archived": boolean,
  "is_transient": boolean,
  "dismissed_cycle_warning": boolean,
  "max_credits_per_run": number
}

```

### Delete Workflow

```markdown
DELETE /api/common/workspaces/{workspace_id}/workflows/{workflow_id}

Response 204

```

## Workflow Snapshots

### Get Workflow Snapshot List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_snapshot_id": "string",
      "parent_workflow_snapshot_id": "string",
      "is_touched": boolean,
      "entity_definition_id": "string",
      "status": "string",
      "published": "string",
      "first_published": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string"
    }
  ]
}

```

### Get Workflow Snapshot

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_snapshot_id": "string",
  "parent_workflow_snapshot_id": "string",
  "is_touched": boolean,
  "entity_definition_id": "string",
  "status": "string",
  "published": "string",
  "first_published": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

## Workflow Blocks

### Get Workflow Block List

```
GET /api/common/workspaces/{workspace_id}/workflow-blocks

```

Response 200:

```json
{
  "accurate_at": "2024-08-09T22:56:41.528464000Z",
  "value": [
    {
      "workspace_id": "8c2d6900-f611-4456-b208-36af7641ddab",
      "workflow_block_id": "007dcd37-854a-4f52-8a89-f50fd9b2f572",
      "type": "step",
      "system_block_type": "step-delay",
      "title": "Delay",
      "description": "Pause a workflow execution for a specified amount of time",
      "latest_workflow_block_version_id": "8466aa20-71de-4e62-9dac-1b3ff52526de",
      "developer_app": null,
      "installed_by": {
        "type": "system",
        "id": "workspace-creation"
      },
      "execution_cost": {
        "credits": 1,
        "refund_if_terminal": false,
        "variable": false
      },
      "installed_at": "2024-05-28T01:13:59.151000000Z",
      "accurate_at": "2024-08-09T22:56:41.528464000Z"
    },
    {
      "workspace_id": "8c2d6900-f611-4456-b208-36af7641ddab",
      "workflow_block_id": "02b532e1-f8e6-4e61-b9c0-0a9c43e454e5",
      "type": "step",
      "system_block_type": "step-parse-json",
      "title": "Parse JSON",
      "description": "Parse structured data from a JSON string",
      "latest_workflow_block_version_id": "82ac0e14-8721-4a90-a0de-8ec3771c91ff",
      "developer_app": null,
      "installed_by": {
        "type": "system",
        "id": "workspace-creation"
      },
      "execution_cost": {
        "credits": 1,
        "refund_if_terminal": false,
        "variable": false
      },
      "installed_at": "2024-05-28T01:13:59.855000000Z",
      "accurate_at": "2024-08-09T22:56:41.528464000Z"
    }
  ]
}

```

This response includes an array of workflow blocks, each containing detailed information such as:

- `workspace_id`: The unique identifier for the workspace (e.g., "8c2d6900-f611-4456-b208-36af7641ddab")
- `workflow_block_id`: The unique identifier for the workflow block (e.g., "007dcd37-854a-4f52-8a89-f50fd9b2f572")
- `type`: The type of the workflow block (e.g., "step", "trigger")
- `system_block_type`: The specific type of system block (e.g., "step-delay", "step-parse-json")
- `title`: The display title of the workflow block (e.g., "Delay", "Parse JSON")
- `description`: A brief description of the workflow block's functionality
- `latest_workflow_block_version_id`: The ID of the latest version of this workflow block
- `developer_app`: Information about the developer app (null if not applicable)
- `installed_by`: Details about who installed the block (typically system-installed)
- `execution_cost`: Information about the cost of executing this block
- `installed_at`: The timestamp when the block was installed
- `accurate_at`: The timestamp indicating when this information was last updated

The response also includes an overall `accurate_at` timestamp for the entire list.

## Workflow Nodes

### Get Workflow Node List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_snapshot_id": "string",
      "workflow_snapshot_node_id": "string",
      "parent_subgraph": "string",
      "description": "string",
      "default_description": "string",
      "execution_cost": {
        "credits": number,
        "refund_if_terminal": boolean,
        "variable": boolean
      },
      "block": {
        "workflow_block_id": "string",
        "workflow_block_version_id": "string"
      },
      "position": {
        "x": number,
        "y": number
      },
      "schema": {
        "last_synced_at": "string",
        "inputs": [
          {
            "slug": "string",
            "title": "string",
            "help_text": "string",
            "tooltip_text": "string",
            "allowed_data_types": ["string"],
            "updates_schema": boolean,
            "is_multi_value": boolean,
            "is_required": boolean,
            "dependant_inputs": ["string"],
            "entity_reference__allowed_entity_definitions": ["string"],
            "currency__configuration": "object",
            "compound_filter__entity_definition_id": "string",
            "disabled": boolean,
            "component": {
              "type": "string",
              "entity_types": ["string"],
              "entity_definition_id": "string",
              "triggerable": boolean,
              "onlyIntegers": boolean,
              "minValue": number,
              "maxValue": number
            }
          }
        ],
        "outputs": [
          {
            "slug": "string",
            "title": "string",
            "data_type": "string",
            "attribute_definition_id": "string"
          }
        ],
        "outcomes": [
          {
            "slug": "string",
            "title": "string"
          }
        ],
        "subgraphs": ["string"]
      },
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string",
      "accurate_at": "string"
    }
  ]
}

```

### Get Workflow Node

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_snapshot_id": "string",
  "workflow_snapshot_node_id": "string",
  "parent_subgraph": "string",
  "description": "string",
  "default_description": "string",
  "execution_cost": {
    "credits": number,
    "refund_if_terminal": boolean,
    "variable": boolean
  },
  "block": {
    "workflow_block_id": "string",
    "workflow_block_version_id": "string"
  },
  "position": {
    "x": number,
    "y": number
  },
  "schema": {
    "last_synced_at": "string",
    "inputs": [
      {
        "slug": "string",
        "title": "string",
        "help_text": "string",
        "tooltip_text": "string",
        "allowed_data_types": ["string"],
        "updates_schema": boolean,
        "is_multi_value": boolean,
        "is_required": boolean,
        "dependant_inputs": ["string"],
        "entity_reference__allowed_entity_definitions": ["string"],
        "currency__configuration": "object",
        "compound_filter__entity_definition_id": "string",
        "disabled": boolean,
        "component": {
          "type": "string",
          "entity_types": ["string"],
          "entity_definition_id": "string",
          "triggerable": boolean,
          "onlyIntegers": boolean,
          "minValue": number,
          "maxValue": number
        }
      }
    ],
    "outputs": [
      {
        "slug": "string",
        "title": "string",
        "data_type": "string",
        "attribute_definition_id": "string"
      }
    ],
    "outcomes": [
      {
        "slug": "string",
        "title": "string"
      }
    ],
    "subgraphs": ["string"]
  },
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "accurate_at": "string"
}

```

### Create/Update Workflow Node

```markdown
PUT /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}

Request:
{
  "parent_subgraph": "string",
  "block": {
    "workflow_block_id": "string",
    "workflow_block_version_id": "string"
  },
  "position": {
    "x": number,
    "y": number
  }
}

Response 200:
{
  // Same as Get Workflow Node response
}

```

### Delete Workflow Node

```markdown
DELETE /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}

Response 204

```

### Get Workflow Node Input Values

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}/input-values

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_snapshot_id": "string",
      "workflow_snapshot_node_id": "string",
      "workflow_snapshot_node_input_value_id": "string",
      "input_slug": "string",
      "sort_order": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string",
      "accurate_at": "string",
      "type": "string",
      "static_value": "any",
      "path": [
        {
          "entity_definition_id": "string",
          "attribute_definition_id": "string"
        }
      ],
      "field": "string",
      "data_type": "string",
      "blocks": [
        {
          "block_type": "string",
          "plaintext": "string",
          "styles": [],
          "variables": []
        }
      ],
      "entity_definition_id": "string",
      "entity_filter_id": "string"
    }
  ]
}

```

### Update Workflow Node Input Value

```markdown
PUT /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}/input-values/{input_value_id}

Request:
{
  "input_slug": "string",
  "type": "string",
  "static_value": "any",
  "data_type": "string",
  "sort_order": "string",
  "entity_definition_id": "string",
  "entity_filter_id": "string"
}

Response 200:
{
  // Same as single input value object in Get Workflow Node Input Values response
}

```

### Sync Workflow Node Schema

```markdown
POST /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/{node_id}/sync-schema

Request:
{}

Response 200:
{
  // Same as Get Workflow Node response
}

```

### Batch Get Workflow Node Input Values

```markdown
POST /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/nodes/batch-list-input-values

Request:
{
  "workflow_snapshot_node_ids": ["string"]
}

Response 200:
{
  "node_id": {
    "state": "string",
    "value": {
      "accurate_at": "string",
      "value": [
        // Array of input value objects, same as in Get Workflow Node Input Values response
      ]
    }
  }
}

```

## Workflow Edges

### Get Workflow Edge List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/edges

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_snapshot_id": "string",
      "workflow_snapshot_edge_id": "string",
      "source": {
        "workflow_snapshot_node_id": "string",
        "outcome": "string"
      },
      "target": {
        "workflow_snapshot_node_id": "string"
      },
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string",
      "accurate_at": "string"
    }
  ]
}

```

### Get Workflow Edge

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/edges/{edge_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_snapshot_id": "string",
  "workflow_snapshot_edge_id": "string",
  "source": {
    "workflow_snapshot_node_id": "string",
    "outcome": "string"
  },
  "target": {
    "workflow_snapshot_node_id": "string"
  },
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "accurate_at": "string"
}

Response 404:
{
  "statusCode": 404,
  "error": "Not Found",
  "message": "The requested Workflow Snapshot Edge could not be found",
  "status_code": 404
}

```

### Create/Update Workflow Edge

```markdown
PUT /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/edges/{edge_id}

Request:
{
  "source": {
    "workflow_snapshot_node_id": "string",
    "outcome": "string"
  },
  "target": {
    "workflow_snapshot_node_id": "string"
  }
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_snapshot_id": "string",
  "workflow_snapshot_edge_id": "string",
  "source": {
    "workflow_snapshot_node_id": "string",
    "outcome": "string"
  },
  "target": {
    "workflow_snapshot_node_id": "string"
  },
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string",
  "accurate_at": "string"
}

```

### Delete Workflow Edge

```markdown
DELETE /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/snapshots/{snapshot_id}/edges/{edge_id}

Response 204

```

## Workflow Runs

### Get Workflow Run List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/runs

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_run_id": "string",
      "workflow_snapshot_id": "string",
      "status": "string",
      "started_at": "string",
      "completed_at": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string"
    }
  ]
}

```

### Get Workflow Run

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/runs/{run_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_run_id": "string",
  "workflow_snapshot_id": "string",
  "status": "string",
  "started_at": "string",
  "completed_at": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

## Workflow Templates

### Get Workflow Template List

```markdown
GET /api/common/workspaces/{workspace_id}/workflow-templates

Response 200:
{
  "accurate_at": "2024-08-10T17:57:31.720Z",
  "value": [
    {
      "workspace_id": "string",
      "workflow_template_id": "string",
      "title": "string",
      "description": "string",
      "categories": [
        "string"
      ],
      "schema": {
        "nodes": [
          {
            "slug": "string",
            "description": "string",
            "block": {
              "type": "string",
              "system_block_type": "string"
            },
            "position": {
              "x": number,
              "y": number
            },
            "inputs": {
              "entity_definition_id": [
                {
                  "type": "string",
                  "reference_type": "string",
                  "entity_type": "string",
                  "standard_object_type": "string"
                }
              ],
              "attribute_definition_id": [
                {
                  "type": "string",
                  "reference_type": "string",
                  "entity_type": "string",
                  "standard_object_type": "string",
                  "system_attribute_type": "string"
                }
              ],
              "condition": [
                {
                  "type": "string",
                  "filter": {
                    "type": "string",
                    "path": [
                      {
                        "node_slug": "string",
                        "output_slug": "string"
                      }
                    ],
                    "mode": "string",
                    "constraints": [
                      {
                        "field": "string",
                        "operator": "string",
                        "value_type": "string",
                        "value": {
                          "type": "string",
                          "reference_type": "string",
                          "entity_type": "string",
                          "standard_object_type": "string",
                          "system_attribute_type": "string",
                          "pipeline_stage_title": "string"
                        }
                      }
                    ]
                  }
                }
              ]
            },
            "outcomes": {
              "triggered": "string",
              "true": "string",
              "false": {
                "node": "string",
                "label": "string"
              },
              "complete": "string"
            }
          }
        ],
        "notes": [
          {
            "blocks": [
              {
                "plaintext": "string",
                "styles": []
              }
            ],
            "position": {
              "x": number,
              "y": number
            },
            "size": {
              "width": number,
              "height": number
            },
            "variant": "string"
          }
        ]
      },
      "accurate_at": "2024-08-10T17:57:31.720Z"
    }
  ]
}

```

## Workflow Memberships

### Get Workflow Membership List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/memberships

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_membership_id": "string",
      "user": {
        "type": "string",
        "id": "string"
      },
      "role": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string"
    }
  ]
}

```

### Get Workflow Membership

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/memberships/{membership_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_membership_id": "string",
  "user": {
    "type": "string",
    "id": "string"
  },
  "role": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

### Create/Update Workflow Membership

```markdown
PUT /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/memberships/{membership_id}

Request:
{
  "user": {
    "type": "string",
    "id": "string"
  },
  "role": "string"
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_membership_id": "string",
  "user": {
    "type": "string",
    "id": "string"
  },
  "role": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

### Delete Workflow Membership

```markdown
DELETE /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/memberships/{membership_id}

Response 204

```

## Workflow Notes

### Get Workflow Note List

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/notes

Response 200:
{
  "accurate_at": "string",
  "value": [
    {
      "workspace_id": "string",
      "workflow_id": "string",
      "workflow_note_id": "string",
      "content": "string",
      "created_by": {
        "type": "string",
        "id": "string"
      },
      "created_at": "string"
    }
  ]
}

```

### Get Workflow Note

```markdown
GET /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/notes/{note_id}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_note_id": "string",
  "content": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

### Create Workflow Note

```markdown
POST /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/notes

Request:
{
  "content": "string"
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_note_id": "string",
  "content": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

### Update Workflow Note

```markdown
PATCH /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/notes/{note_id}

Request:
{
  "content": "string"
}

Response 200:
{
  "workspace_id": "string",
  "workflow_id": "string",
  "workflow_note_id": "string",
  "content": "string",
  "created_by": {
    "type": "string",
    "id": "string"
  },
  "created_at": "string"
}

```

### Delete Workflow Note

```markdown
DELETE /api/common/workspaces/{workspace_id}/workflows/{workflow_id}/notes/{note_id}

Response 204

```