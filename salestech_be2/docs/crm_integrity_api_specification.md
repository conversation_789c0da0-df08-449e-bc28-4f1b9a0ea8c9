# CRM Integrity API Specification

## Summary of Resources

1. **Replace Contact ID in Activities**
    - **POST** `/crm_integrity/replace_contact_id_in_activities`
    - **Description**: Replace the contact ID in the metadata of activities.
    - **Request Body**:
        - `contact_id`: The contact ID to replace.
        - `replaced_contact_id`: The new contact ID to replace the old contact ID with.

## API Endpoints

### CRM Integrity Job


- POST /crm_integrity_job/_preview
    - used for preview a merge job
    - Request body:
        
        ```json
{
    "type": "MERGE",
    "src_entity_type": "CONTACT",
    "src_entity_id": "eb3a5a1d-e4b8-4a37-813d-2cddcb4576d1",
    "dest_entity_type": "CONTACT",
    "dest_entity_id": "85af9ec1-4d70-454a-ad08-2b7d7e41349f",
    "organization_id": "b7b8bf65-1281-4ac6-94d4-46b9e8402925",
    "user_id": "a670762f-068e-455e-97dc-50416cd65142"
}
        ```
        
    - Response body
        
        ```json
        {
          "id": "<uuid>",
          "name": "<string>",
          "type": "<string>",
          "description": "<string>"
        }
        ```

- POST /crm_integrity_job
    - used for create a merge job
    - Request body:
        
        ```json
        {
            "type": "MERGE",
            "src_entity_type": "CONTACT",
            "src_entity_id": "eb3a5a1d-e4b8-4a37-813d-2cddcb4576d1",
            "dest_entity_type": "CONTACT",
            "dest_entity_id": "85af9ec1-4d70-454a-ad08-2b7d7e41349f",
            "organization_id": "b7b8bf65-1281-4ac6-94d4-46b9e8402925",
            "user_id": "a670762f-068e-455e-97dc-50416cd65142"
        }
        ```
        
    - Response body
        
        ```json
        {
          "id": "<uuid>",
          "name": "<string>",
          "type": "<string>",
          "description": "<string>"
        }
        ```
        
- GET /crm_integrity_job/{job_id}
    - Request body: N/A
    - Response body
        
        ```json
        {
          "id": "<uuid>",
          "name": "<string>",
          "type": "<string>",
          "description": "<string>",
          "sequence_id": "<uuid>" | null
        }
        ```
