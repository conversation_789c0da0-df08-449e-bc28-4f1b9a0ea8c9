import os

from temporalio.service import TLSConfig

TEMPORAL_MTLS_TLS_CERT = os.getenv("SALESTECH_BE_TEMPORAL_MTLS_TLS_CERT")
TEMPORAL_MTLS_TLS_KEY = os.getenv("SALESTECH_BE_TEMPORAL_MTLS_TLS_KEY")
TEMPORAL_HOST_URL = os.getenv("SALESTECH_BE_TEMPORAL_HOST_URL", "localhost:7233")
TEMPORAL_NAMESPACE = os.getenv("SALESTECH_BE_TEMPORAL_NAMESPACE", "default")
DEFAULT_TASK_QUEUE = "default-task4-queue"

client_cert = None
client_key = None

if TEMPORAL_MTLS_TLS_CERT:
    with open(TEMPORAL_MTLS_TLS_CERT, "rb") as f:
        client_cert = f.read()

if TEMPORAL_MTLS_TLS_KEY:
    with open(TEMPORAL_MTLS_TLS_KEY, "rb") as f:
        client_key = f.read()

tls: TLSConfig | bool = False
if client_cert and client_key:
    tls = TLSConfig(
        client_cert=client_cert,
        client_private_key=client_key,
    )
