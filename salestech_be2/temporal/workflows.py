# WARNING: Do not import anything that touches network, filesystem, or any non-deterministic
# operations in activities. This can lead to non-deterministic behavior in workflows.
# Only import pure functions or deterministic operations in workflows.
from datetime import timedelta

# Import activity, passing it through the sandbox without reloading the module
from temporalio import workflow
from temporalio.common import RetryPolicy

with workflow.unsafe.imports_passed_through():
    from salestech_be.core.workflow.service.workflow_activity_service import (
        WorkflowActivityService,
        get_activity_by_block_name,
    )
    from salestech_be.core.workflow.types.schema import (
        EndRunParam,
        ExecuteNodeParam,
        FindNextNodeParam,
        StartRunParam,
    )
    from salestech_be.db.models.workflow import WorkflowRun, WorkflowRunNodeStatus
    from salestech_be.temporal.activities import say_hello


retry_policy = RetryPolicy(
    maximum_interval=timedelta(seconds=60),
    non_retryable_error_types=["WorkflowFailError"],
    maximum_attempts=0,  # retry forever for now
)


@workflow.defn
class SayHello:
    @workflow.run
    async def run(self, name: str) -> str:
        return await workflow.execute_activity(
            say_hello, name, start_to_close_timeout=timedelta(seconds=5)
        )


@workflow.defn
class WorkflowExecution:
    @workflow.run
    async def run(self, start_run_param: StartRunParam) -> WorkflowRun | None:
        await workflow.execute_activity_method(
            WorkflowActivityService.start_run,
            start_run_param,
            start_to_close_timeout=timedelta(seconds=5),
            retry_policy=retry_policy,
        )

        execute_node = ExecuteNodeParam(
            organization_id=start_run_param.organization_id,
            workflow_id=start_run_param.workflow_id,
            snapshot_id=start_run_param.snapshot_id,
            node_id=start_run_param.node_id,
            node_name=start_run_param.node_name,
            block_name=start_run_param.block_name,
            run_id=start_run_param.run_id,
        )

        while True:
            # Execute current node
            execute_result = await workflow.execute_activity_method(
                get_activity_by_block_name(execute_node.block_name),
                execute_node,
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=retry_policy,
            )
            if execute_result.status != WorkflowRunNodeStatus.SUCCESS:
                break

            # Find next node
            workflow_node = await workflow.execute_activity_method(
                WorkflowActivityService.find_next_node,
                FindNextNodeParam(
                    organization_id=execute_node.organization_id,
                    workflow_id=execute_node.workflow_id,
                    snapshot_id=execute_node.snapshot_id,
                    node_id=execute_node.node_id,
                    node_name=execute_node.node_name,
                    run_id=execute_node.run_id,
                    outcome=execute_result.outcome,
                ),
                start_to_close_timeout=timedelta(seconds=5),
                retry_policy=retry_policy,
            )
            if not workflow_node:
                break

            execute_node = ExecuteNodeParam(
                organization_id=workflow_node.organization_id,
                workflow_id=workflow_node.workflow_id,
                snapshot_id=workflow_node.snapshot_id,
                node_id=workflow_node.id,
                node_name=workflow_node.name,
                block_name=workflow_node.block_name,
                run_id=start_run_param.run_id,
            )

        return await workflow.execute_activity_method(
            WorkflowActivityService.end_run,
            EndRunParam(
                run_id=execute_node.run_id,
                status=execute_result.status,
            ),
            start_to_close_timeout=timedelta(seconds=5),
            retry_policy=retry_policy,
        )
