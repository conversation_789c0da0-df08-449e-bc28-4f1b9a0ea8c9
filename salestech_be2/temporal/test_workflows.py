# WARNING: Do not import anything that touches network, filesystem, or any non-deterministic
# operations in activities. This can lead to non-deterministic behavior in workflows.
# Only import pure functions or deterministic operations in workflows.
from datetime import timedelta
from temporalio import workflow
from temporalio.common import RetryPolicy

# Import activity, passing it through the sandbox without reloading the module
with workflow.unsafe.imports_passed_through():
    from salestech_be.integrations.temporal.errors import WorkflowFailError
    from salestech_be2.temporal.test_activities import (
        test_find_next_node,
        test_execute_node,
        get_activity_by_node, test_sleep_node,
)


@workflow.defn
class SleepExecution:
    @workflow.run
    async def run(self, node: int = 0) -> int:
        return await workflow.execute_activity(
            test_sleep_node,
            node,
            start_to_close_timeout=timedelta(seconds=30),
            retry_policy=RetryPolicy(
                maximum_interval=timedelta(seconds=3),
                maximum_attempts=3,
                non_retryable_error_types=["WorkflowFailError"],
            ),
        )


@workflow.defn
class TestExecution:
    @workflow.run
    async def run(self, node: int = 0) -> int:
        while True:
            result = await workflow.execute_activity(
                test_execute_node,
                node,
                start_to_close_timeout=timedelta(seconds=30),
                retry_policy=RetryPolicy(
                    maximum_interval=timedelta(seconds=3),
                    maximum_attempts=10,
                    non_retryable_error_types=["WorkflowFailError"],
                ),
            )

            node = await workflow.execute_activity(
                test_find_next_node,
                node,
                start_to_close_timeout=timedelta(seconds=30)
            )
            if node is None:
                return result


@workflow.defn
class MappingExecution:
    @workflow.run
    async def run(self, node: int = 0) -> int | None:
        while True:
            node_activity = get_activity_by_node(node)
            result = await workflow.execute_activity(
                node_activity,
                node,
                start_to_close_timeout=timedelta(seconds=30)
            )

            node = await workflow.execute_activity(
                test_find_next_node,
                node,
                start_to_close_timeout=timedelta(seconds=30)
            )
            if node is None:
                return result
