import datetime
import random

import fire
import asyncio
from uuid import uuid4, UUID

import temporalio.service

from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
from salestech_be2.temporal.test_workflows import (
    TestExecution,
    MappingExecution,
)


async def test():
    client = await get_temporal_client()
    run_id = uuid4()
    result = await client.execute_workflow(
        TestExecution.run,
        0,
        id=str(run_id),
        task_queue="LOCAL_TEST_QUEUE",
    )
    print(result)


async def sleep():
    client = await get_temporal_client()
    run_id = uuid4()
    result = await client.execute_workflow(
        TestExecution.run,
        10,
        id=str(run_id),
        task_queue="LOCAL_TEST_QUEUE",
    )
    print(result)


async def mapping():
    client = await get_temporal_client()
    run_id = uuid4()
    result = await client.execute_workflow(
        MappingExecution.run,
        0,
        id=str(run_id),
        task_queue="LOCAL_TEST_QUEUE",
    )
    print(result)


async def schedule():
    from datetime import timedelta
    from temporalio.client import (
        Client,
        Schedule,
        ScheduleActionStartWorkflow,
        ScheduleAlreadyRunningError,
        ScheduleIntervalSpec,
        ScheduleSpec,
        ScheduleState,
        SchedulePolicy,
        ScheduleOverlapPolicy,
        ScheduleCalendarSpec,
    )
    from salestech_be.temporal.workflows.say_hello import SayHello
    client = await get_temporal_client()
    await client.create_schedule(
        "hello-world-schedule-id",
        Schedule(
            action=ScheduleActionStartWorkflow(
                SayHello.run,
                "It's Yo Daddy",
                id="schedules-workflow-id",
                task_queue=DEFAULT_TASK_QUEUE,
            ),
            spec=ScheduleSpec(
                calendars=[ScheduleCalendarSpec(every=timedelta(minutes=2))]
            ),
            state=ScheduleState(note="Here's a note on my Schedule."),
            policy=SchedulePolicy(
                overlap=ScheduleOverlapPolicy.ALLOW_ALL
            ),
        ),
    )


async def create_one_time_schedule():
    from temporalio.client import (
        Schedule,
        ScheduleActionStartWorkflow,
        ScheduleSpec,
        ScheduleState,
        ScheduleCalendarSpec,
        ScheduleRange,
    )
    from salestech_be.temporal.workflows.say_hello import SayHello
    client = await get_temporal_client()
    await client.create_schedule(
        "one-time-schedule",
        Schedule(
            action=ScheduleActionStartWorkflow(
                SayHello.run,
                "It's Yo Daddy",
                id="one-time-schedule-id",
                task_queue=DEFAULT_TASK_QUEUE,
            ),
            spec=ScheduleSpec(
                calendars=[
                    ScheduleCalendarSpec(
                        year=[ScheduleRange(start=2024)],
                        month=[ScheduleRange(start=10)],
                        day_of_month=[ScheduleRange(start=16)],
                        hour=[ScheduleRange(start=11)],
                        minute=[ScheduleRange(start=45)],
                        second=[ScheduleRange(start=0)],
                    )
                ]
            ),
            state=ScheduleState(note="Here's a note on my Schedule."),
        ),
    )


async def create_one_time_workflow():
    from salestech_be.temporal.workflows.say_hello import SayHello
    client = await get_temporal_client()
    await client.start_workflow(
        SayHello.run,
        "It's Yo Daddy",
        id=f"one_time_workflow",
        task_queue=DEFAULT_TASK_QUEUE,
        start_delay=datetime.timedelta(minutes=2),
    )


async def create_cron_schedule():
    from temporalio.client import (
        Schedule,
        ScheduleActionStartWorkflow,
        ScheduleSpec,
        ScheduleState,
        SchedulePolicy,
        ScheduleOverlapPolicy,
    )
    from salestech_be.temporal.workflows.say_hello import SayHello
    client = await get_temporal_client()
    await client.create_schedule(
        "cron-schedule",
        Schedule(
            action=ScheduleActionStartWorkflow(
                SayHello.run,
                "It's Yo Daddy",
                id="cron-schedule-id",
                task_queue=DEFAULT_TASK_QUEUE,
            ),
            spec=ScheduleSpec(
                cron_expressions=["*/5 * * * *"]
            ),
            state=ScheduleState(note="Here's a note on my Schedule."),
            policy=SchedulePolicy(
                overlap=ScheduleOverlapPolicy.ALLOW_ALL
            ),
        ),
    )


async def get_schedule():
    try:
        client = await get_temporal_client()
        handle = client.get_schedule_handle(
            "trigger_workflow_time_generator",
        )
        desc = await handle.describe()
        print(f"Returns the note: {desc}")
    except temporalio.service.RPCError:
        print("None")
        return None


if __name__ == "__main__":
    fire.Fire()
