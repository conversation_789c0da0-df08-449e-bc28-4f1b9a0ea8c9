import asyncio
from collections.abc import <PERSON>wai<PERSON>, Callable, Coroutine
from functools import wraps
from typing import Any, ParamSpec, TypeVar
from temporalio import activity
from salestech_be.integrations.temporal.errors import log_error


@activity.defn
@log_error
async def test_find_next_node(node: int) -> int | None:
    if node >= 2:
        return None
    return node + 1


@activity.defn
@log_error
async def test_execute_node(node: int) -> int:
    print(node, activity.info().attempt)
    if node == 1:
        raise RuntimeError('bug')
    return node


@activity.defn
@log_error
async def test_sleep_node(node: int) -> int:
    await asyncio.sleep(node)
    return node


class BaseActivity:
    async def execute(self, node):
        await self.before(node)
        await self.process(node)
        await self.after(node)

    async def before(self, node):
        print(f"Before {node}")

    async def process(self, node):
        print(f"Process {node}")

    async def after(self, node):
        print(f"After {node}")


class SwitchActivity(BaseActivity):
    @activity.defn(name="switch_activity")
    async def execute(self, node):
        print(f"Switch {node}")


async def execute_activity(node: int) -> int | None:
    print(node)
    return node


@activity.defn
async def form_submission_activity(node: int) -> int | None:
    return await execute_activity(node)


@activity.defn
async def switch_statement_activity(node: int) -> int | None:
    return await execute_activity(node)


@activity.defn
async def if_else_statement_activity(node: int) -> int | None:
    return await execute_activity(node)


node_activities = [
    form_submission_activity,
    switch_statement_activity,
    if_else_statement_activity,
]


def get_activity_by_node(node: int):
    print(node)
    if node < len(node_activities):
        return node_activities[node]
    else:
        raise RuntimeError('unknown node')
