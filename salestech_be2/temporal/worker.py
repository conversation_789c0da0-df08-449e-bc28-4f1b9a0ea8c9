import asyncio
import dataclasses

from temporalio import workflow
from temporalio.worker import Worker
from temporalio.worker.workflow_sandbox import (
    SandboxedWorkflowRunner,
    SandboxRestrictions,
)


with workflow.unsafe.imports_passed_through():
    from salestech_be.ree_logging import get_logger
    from salestech_be.task.instantiate.database import get_db_engine
    from salestech_be.integrations.temporal.client import get_temporal_client
    from salestech_be.integrations.temporal.config import DEFAULT_TASK_QUEUE
    from salestech_be2.temporal.test_workflows import SleepExecution, TestExecution
    from salestech_be2.temporal.test_activities import test_sleep_node, \
        test_execute_node

logger = get_logger()
interrupt_event = asyncio.Event()


# https://github.com/temporalio/samples-python/blob/main/pydantic_converter/worker.py
# Due to known issues with Pydantic's use of issubclass and our inability to
# override the check in sandbox, Pydantic will think datetime is actually date
# in the sandbox. At the expense of protecting against datetime.now() use in
# workflows, we're going to remove datetime module restrictions. See sdk-python
# README's discussion of known sandbox issues for more details.
def new_sandbox_runner() -> SandboxedWorkflowRunner:
    # TODO(cretz): Use with_child_unrestricted when https://github.com/temporalio/sdk-python/issues/254
    # is fixed and released
    invalid_module_member_children = dict(
        SandboxRestrictions.invalid_module_members_default.children
    )
    del invalid_module_member_children["datetime"]
    return SandboxedWorkflowRunner(
        restrictions=dataclasses.replace(
            SandboxRestrictions.default,
            invalid_module_members=dataclasses.replace(
                SandboxRestrictions.invalid_module_members_default,
                children=invalid_module_member_children,
            ),
        )
    )


async def main() -> None:
    client = await get_temporal_client()
    async with Worker(
        client,
        task_queue="LOCAL_TEST_QUEUE",
        workflows=[
            SleepExecution,
            TestExecution,
        ],
        activities=[
            test_sleep_node,
            test_execute_node,
        ],
        workflow_runner=new_sandbox_runner(),
    ):
        # Wait until interrupted
        await interrupt_event.wait()


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    try:
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        interrupt_event.set()
        loop.run_until_complete(loop.shutdown_asyncgens())
