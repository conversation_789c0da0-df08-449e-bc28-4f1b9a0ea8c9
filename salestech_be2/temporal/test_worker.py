import asyncio
from temporalio.worker import Worker

from salestech_be.integrations.temporal.client import get_temporal_client
from salestech_be2.temporal.test_workflows import (
    TestExecution,
    MappingExecution,
    SleepExecution,
)
from salestech_be2.temporal.test_activities import (
    test_sleep,
    test_find_next_node,
    test_execute_node,
    node_activities,
    SwitchActivity,
)


async def main() -> None:
    client = await get_temporal_client()
    # Run the worker
    switch_activity = SwitchActivity()

    worker = Worker(
        client,
        task_queue="LOCAL_TEST_QUEUE",
        workflows=[
            TestExecution,
            MappingExecution,
            SleepExecution,
        ],
        activities=[
            test_sleep,
            test_execute_node,
            test_find_next_node,
            *node_activities,
            switch_activity.execute,
        ],
    )
    await worker.run()


if __name__ == "__main__":
    asyncio.run(main())
