import asyncio
import sys
from uuid import uuid4, UUID

from salestech_be.db.dao.workflow_repository import WorkflowNodeRepository
from salestech_be.util.time import zoned_utc_now
from salestech_be.temporal.worker import DEFAULT_TASK_QUEUE
from salestech_be.core.workflow.types.schema import StartRunParam
from salestech_be.temporal.workflows.reevo_workflow import ReevoWorkflow
from salestech_be.temporal.client import get_client
from salestech_be.task.instantiate.database import get_db_engine


async def main(snapshot_id):
    run_id = uuid4()
    event_id = UUID("99127252-91eb-4119-9048-************")
    orgnization_id = UUID("c6734584-b3f3-4608-b62e-a993f4703a8e")
    workflow_id = UUID("a84939cf-01fd-4adb-a5ea-0dabd3c91ed7")
    snapshot_id = UUID(snapshot_id)

    db_engine = await get_db_engine()
    workflow_node_repo = WorkflowNodeRepository(engine=db_engine)
    trigger_node = await workflow_node_repo.find_trigger_node_by_snapshot_id(
        organization_id=orgnization_id,
        workflow_id=workflow_id,
        snapshot_id=snapshot_id,
    )

    client = await get_client()
    workflow_run = StartRunParam(
        organization_id=orgnization_id,
        workflow_id=workflow_id,
        snapshot_id=snapshot_id,
        snapshot_name="test",
        node_id=trigger_node.id,
        node_name=trigger_node.name,
        block_name=trigger_node.block_name,
        run_id=run_id,
        event_id=event_id,
        event_time=int(zoned_utc_now().timestamp()),
    )

    # Execute a workflow asynchronously
    handle = await client.start_workflow(
        ReevoWorkflow.run,
        workflow_run,
        id=str(run_id),
        task_queue=DEFAULT_TASK_QUEUE,
    )

    print(f"Started workflow. Workflow ID: {handle.id}, RunID {handle.result_run_id}")
    result = await handle.result()
    print(f"Result: {result}")


if __name__ == "__main__":
    asyncio.run(main("1d697766-0d0e-4c34-8d84-8f9f4a192136"))
