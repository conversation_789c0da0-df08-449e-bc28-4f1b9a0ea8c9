from collections.abc import Awaitable, Callable
from functools import wraps
from typing import ParamSpec, TypeVar

from salestech_be.ree_logging import get_logger

logger = get_logger()

P = ParamSpec("P")
T = TypeVar("T")


def log_error(func: Callable[P, Awaitable[T]]) -> Callable[P, Awaitable[T]]:
    @wraps(func)
    async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        try:
            return await func(*args, **kwargs)
        except Exception as err:
            logger.error(err)
            raise err

    return wrapper


class WorkflowFailError(Exception):
    """Raised when non-retryable error occurred."""
