import asyncio

from salestech_be.temporal.config import TEMPORAL_HOST_URL, TEMPORAL_NAMESPACE, tls
from salestech_be.temporal.converter import pydantic_data_converter
from temporalio.client import Client

_client: Client | None = None
_lock = asyncio.Lock()


async def get_client() -> Client:
    global _client  # noqa: PLW0603
    async with _lock:
        if _client is None:
            _client = await Client.connect(
                target_host=TEMPORAL_HOST_URL,
                namespace=TEMPORAL_NAMESPACE,
                tls=tls,
                data_converter=pydantic_data_converter,
            )
        return _client
