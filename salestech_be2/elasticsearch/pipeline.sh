curl -X DELETE "http://localhost:9200/pipelines"

# define the contact mapping
curl -X PUT "http://localhost:9200/pipelines" -H 'Content-Type: application/json' -d'
{
    "mappings": {
        "properties": {
            "pipeline_id": {"type": "keyword"},
            "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "account": {
                "type": "object",
                "properties": {
                    "name": {"type": "text"},
                    "last_updated_at": {"type": "date"},
                    "estimated_employee_count": {"type": "integer"},
                    "estimated_annual_revenue": {"type": "float"},
                    "categories": {"type": "keyword"}
                }
            },
            "primary_contact": {
                "type": "object",
                "properties": {
                    "name": {"type": "text"},
                    "last_updated_at": {"type": "date"},
                    "primary_account": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "text"},
                            "estimated_employee_count": {"type": "integer"},
                            "estimated_annual_revenue": {"type": "float"},
                            "categories": {"type": "keyword"}
                        }
                    }
                }
            },
            "tasks": {
                "type": "nested",
                "properties": {
                    "title": {"type": "text"},
                    "due_date": {"type": "date"},
                    "status": {"type": "keyword"},
                    "last_updated_at": {"type": "date"},
                    "attendee_contact_ids": {"type": "keyword"},
                    "attendee_user_ids": {"type": "keyword"}
                }
            }
        }
    }
}
'

# create a pipeline
curl -X POST "http://localhost:9200/pipelines/_doc" -H 'Content-Type: application/json' -d'
{
  "pipeline_id": "3",
  "name": "Jane Pit",
  "last_updated_at": "2024-01-01",
  "updated_by_user_id": "123",
  "account": {
    "name": "Acme, Inc.",
    "last_updated_at": "2024-01-01",
    "estimated_employee_count": 100,
    "estimated_annual_revenue": 1000000,
    "categories": ["Technology", "Software"]
  },
  "primary_contact": {
    "name": "Jane Doe",
    "last_updated_at": "2024-01-01",
    "primary_account": {
      "name": "Acme, Inc.",
      "estimated_employee_count": 100,
      "estimated_annual_revenue": 1000000,
      "categories": ["Technology", "Software"]
    }
  },
  "tasks": [
    {
      "title": "Meeting with Jane Doe",
      "due_date": "2026-01-01",
      "status": "in_progress",
      "last_updated_at": "2024-01-01",
      "attendee_contact_ids": ["123", "456"],
      "attendee_user_ids": ["789", "101"]
    },
    {
      "title": "Meeting with Jane Doe",
      "due_date": "2027-01-01",
      "status": "in_progress",
      "last_updated_at": "2024-01-02",
      "attendee_contact_ids": ["123", "789"],
      "attendee_user_ids": ["101", "456"]
    }
  ]
}
'

# GET all pipelines
curl -X GET "http://localhost:9200/pipelines/_search"

# find all pipelines whose next due task has attendee_contact_id "456"
curl -X GET "http://localhost:9200/pipelines/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "nested": {
      "path": "tasks",
      "query": {
        "bool": {
          "must": [
            {
              "range": {
                "tasks.due_date": {
                  "gte": "now"
                }
              }
            },
            {
              "term": {
                "tasks.attendee_contact_ids": "456"
              }
            }
          ]
        }
      },
      "inner_hits": {
        "sort": [
          {
            "tasks.due_date": {
              "order": "asc"
            }
          }
        ],
        "size": 1
      }
    }
  }
}
'
# search pipelines that has next due task meeting with contact id 789
# and has a task with attendee_contact_ids 123