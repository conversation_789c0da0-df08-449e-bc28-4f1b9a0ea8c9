# delete the account index
curl -X DELETE "http://localhost:9200/accounts"

# define the account mapping
curl -X PUT "http://localhost:9200/accounts" -H 'Content-Type: application/json' -d'
{
    "mappings": {
        "properties": {
            "account_id": {"type": "keyword"},
            "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "estimated_employee_count": {"type": "integer"},
            "estimated_annual_revenue": {"type": "float"},
            "categories": {"type": "keyword"}
        }
    }
}
'

# create a account
curl -X POST "http://localhost:9200/contacts/_doc" -H 'Content-Type: application/json' -d'
{
  "contact_id": "1",
  "name": "<PERSON>",
  "last_updated_at": "2024-01-01",
  "updated_by_user_id": "123",
  "primary_account_id": "456",
  "primary_account": {
    "name": "Acme Corp",
    "last_updated_at": "2024-01-01",
    "updated_by_user_id": "123",
    "estimated_employee_count": 100,
    "estimated_annual_revenue": 1000000,
    "categories": ["Technology", "Software"]
  }
}
'

# Find all contacts
curl -X GET "http://localhost:9200/contacts/_search"

# Find a contact
curl -X GET "http://localhost:9200/contacts/_search?q=contact_id:1"

# Find all contacts whose name contains "John"
curl -X GET "http://localhost:9200/contacts/_search" -H 'Content-Type: application/json' -d'
{
  "query": {
    "match": {
      "name": "John"
    }
  }
}
'
