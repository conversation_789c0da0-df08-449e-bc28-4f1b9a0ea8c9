from __future__ import annotations

from enum import StrEnum
from typing import Literal

from pydantic import BaseModel
from collections import defaultdict


class StdObjectIdentifiers(StrEnum):
    user = "user"
    address = "address"
    account = "account"
    contact = "contact"
    meeting = "meeting"
    meeting_participant = "meeting_participant"
    meeting_bot_status_event = "meeting_bot_status_event"
    deal = "deal"
    lead = "lead"
    pipeline = "pipeline"
    account_summary = "account_summary"
    contact_role_summary = "contact_role_summary"
    deal_summary = "deal_summary"
    lead_summary = "lead_summary"
    account_engagement_contact = "account_engagement_contact"
    deal_funnel = "deal_funnel"
    deal_funnel_stage = "deal_funnel_stage"
    task = "task"
    user_goal = "user_goal"
    user_organization_profile = "user_organization_profile"
    comment = "comment"

CoreCrmStdObjectIdentifiers = Literal[
    StdObjectIdentifiers.account,
    StdObjectIdentifiers.contact,
    StdObjectIdentifiers.pipeline,
]

PropagationRuleAttribute = Literal[
    "stage",
    "state",
]

class LogicalPropagationEndpoint(BaseModel):
    entity_type: CoreCrmStdObjectIdentifiers
    attribute: PropagationRuleAttribute

    def __eq__(self, other: LogicalPropagationEndpoint) -> bool:
        return self is other or self.entity_type.value == other.entity_type.value and self.attribute == other.attribute

    def __hash__(self) -> int:
        return hash(f"{self.entity_type.value}.{self.attribute}")


class TriggerRuleChecker:
    def __init__(self):
        # Dictionary to store the directed graph of rules
        self.graph = defaultdict(list)

    def add_rule(self, source, destination):
        """
        Adds a rule to the graph.
        :param source: Tuple (source_table, source_column)
        :param destination: Tuple (dest_table, dest_column)
        """
        self.graph[source].append(destination)

    def detect_cycle(self):
        """
        Checks if there is a cycle in the current graph.
        :return: True if a cycle exists, False otherwise
        """
        visited = set()
        rec_stack = set()

        def dfs(node):
            print('=========')
            print(node, node not in visited)
            if node not in visited:
                visited.add(node)
                print("visited", visited)
                rec_stack.add(node)
                print("rec_stack", rec_stack)
                if node in self.graph:
                    for neighbor in self.graph[node]:
                        print("neighbor", neighbor)
                        if neighbor not in visited and dfs(neighbor):
                            return True
                        elif neighbor in rec_stack:
                            return True
                rec_stack.remove(node)
            return False

        return any(dfs(node) for node in self.graph)

    def can_add_rule_without_cycle(self, source, destination):
        """
        Temporarily adds a new rule to check if it causes a cycle.
        :param source: Tuple (source_table, source_column)
        :param destination: Tuple (dest_table, dest_column)
        :return: True if the rule can be added without causing a cycle, False otherwise
        """
        # Temporarily add the rule
        self.graph[source].append(destination)
        has_cycle = self.detect_cycle()
        # Remove the temporary rule after checking
        # self.graph[source].pop()
        return not has_cycle


# Example usage
checker = TriggerRuleChecker()

# Existing rules
# checker.add_rule(('source_table1', 'column1'), ('dest_table1', 'column2'))
# checker.add_rule(LogicalPropagationEndpoint(
#     entity_type=StdObjectIdentifiers.contact,
#     attribute="stage",
# ), LogicalPropagationEndpoint(
#     entity_type=StdObjectIdentifiers.account,
#     attribute="stage",
# ))
# checker.add_rule(('dest_table1', 'column2'), ('source_table2', 'column3'))

# New rule to check
source = ('source_table2', 'column3')
source = ('dest_table1', 'column2')
destination = ('source_table1', 'column1')
destination = ('source_table2', 'column3')

if checker.can_add_rule_without_cycle(LogicalPropagationEndpoint(
    entity_type=StdObjectIdentifiers.account,
    attribute="stage",
), LogicalPropagationEndpoint(
    entity_type=StdObjectIdentifiers.account,
    attribute="stage",
)):
    print("The rule can be added without causing a circular update.")
else:
    print("Adding this rule will cause an infinite circular update.")
