from typing import Annotated, assert_never
from uuid import UUID

from fastapi import Depends

from salestech_be.common.exception import InvalidArgumentError
from salestech_be.common.schema_manager.std_object_field_identifier import (
    StdSelectListIdentifier,
)
from salestech_be.common.type.patch_request import specified
from salestech_be.core.common import temp_common_pipeline_funnel
from salestech_be.core.metadata.dto.select_list_dto import (
    PipelineStageDto,
    PipelineStageSelectListDto,
    PipelineStageSelectListMutateDto,
    PipelineStageSelectListValueDto,
    PipelineStageSelectListValueMutateDto,
    PipelineStageSelectListValueReMapDto,
)
from salestech_be.core.metadata.dto.service_api_schema import (
    PipelineStageSelectListCreateRequest,
    PipelineStageSelectListPatchRequest,
    PipelineStageSelectListValueCreateMultiRequest,
    PipelineStageSelectListValueCreateRequest,
    PipelineStageSelectListValueFullPutRequest,
    PipelineStageSelectListValuePatchRequest,
    PipelineStageSelectListValueRerankMultiRequest,
    SelectListValueCreateRequest,
)
from salestech_be.core.metadata.repository.pipeline_stage_select_list_repository import (
    PipelineStageSelectListRepository,
)
from salestech_be.core.metadata.service.select_list_common import SelectListCommonLogic
from salestech_be.core.metadata.service.select_list_service import (
    SelectListService,
    get_select_list_service,
)
from salestech_be.core.metadata.types import PipelineStageSelectListSummary
from salestech_be.db.dbengine.core import DatabaseEngine
from salestech_be.db.models.pipeline_select_list_metadata import (
    PipelineStageSelectListMetadataUpdate,
)
from salestech_be.db.models.select_list import (
    SelectList,
    SelectListStatus,
    SelectListValue,
    SelectListValueStatus,
)
from salestech_be.ree_logging import get_logger, log_context
from salestech_be.util.validation import not_none

logger = get_logger()


class PipelineStageSelectListService:
    def __init__(
        self,
        pipeline_stage_select_list_repository: Annotated[
            PipelineStageSelectListRepository, Depends()
        ],
        select_list_service: Annotated[SelectListService, Depends()],
    ):
        self.pipeline_stage_sl_repo = pipeline_stage_select_list_repository
        self.select_list_service = select_list_service

    async def _get_pipeline_stage_sl_and_validate_modifiable(
        self, *, organization_id: UUID, pipeline_stage_select_list_id: UUID
    ) -> SelectList:
        """
        get and validate select_list
        validation rules:
            created select list is not deleted.
            "application code" is correct.
        """
        pipeline_stage_select_list = (
            await self.select_list_service.get_select_list_and_validate_modifiable(
                select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
            )
        )
        if (
            pipeline_stage_select_list.application_code_name
            != StdSelectListIdentifier.pipeline_stage
        ):
            raise InvalidArgumentError(
                f"Select list is not a pipeline stage. (list_id: {pipeline_stage_select_list_id})"
            )
        return pipeline_stage_select_list

    async def create_pipeline_stage_select_list(
        self,
        *,
        organization_id: UUID,
        actioning_user_id: UUID,
        req: PipelineStageSelectListCreateRequest,
    ) -> PipelineStageDto:
        """
        Create a pipeline stage select list.
        Optionally create its list values if provided in the request.
        """

        # validate the create request contains at least one "is_converted" stage value
        if not any(
            lsslv.is_converted for lsslv in req.list_value_create_multi_req.value_reqs
        ):
            raise InvalidArgumentError(
                "The create request must contain at least one 'is_converted' stage value"
            )

        # DTO converter
        (
            select_list,
            pipeline_stage_select_list_metadata,
        ) = SelectListCommonLogic.map_LSSLCR_to_LSSLM_and_SL(
            lsscr=req,
            actioning_user_id=actioning_user_id,
            application_code_name=StdSelectListIdentifier.pipeline_stage,
            organization_id=organization_id,
        )

        # 1. set is_default = False for all records in pipeline_stage_select_list_metadata
        #    if current newly created pipeline_stage_select_list record's is_default = True
        # 2. save into select_list table
        # 3. save into pipeline_stage_select_list_metadata table
        await self.pipeline_stage_sl_repo.insert_pipeline_stage_sl(
            pipeline_stage_select_list_metadata=pipeline_stage_select_list_metadata,
            select_list=select_list,
            organization_id=organization_id,
        )

        list_value_dtos = tuple(
            [
                await self.add_pipeline_stage_select_list_value(
                    select_list_id=select_list.id,
                    actioning_user_id=actioning_user_id,
                    organization_id=organization_id,
                    req=list_value_req,
                )
                for list_value_req in req.list_value_create_multi_req.value_reqs
            ]
        )

        activated_pipeline_stage_sl_mutate_dto = (
            await self.activate_pipeline_stage_select_list(
                pipeline_stage_select_list_id=select_list.id,
                actioning_user_id=actioning_user_id,
                organization_id=organization_id,
            )
        )

        if len(list_value_dtos) > 1:
            # When there are multiple values created,
            # every subsequently creation will affect prior values
            # to simplify (or cheat), just get the whole object in this case.
            return await self.get_pipeline_stage_select_list(
                select_list_id=select_list.id, organization_id=organization_id
            )

        return PipelineStageDto(
            select_list_dto=activated_pipeline_stage_sl_mutate_dto,
            select_list_value_dtos=list_value_dtos,
        )

    async def add_pipeline_stage_select_list_value(
        self,
        *,
        actioning_user_id: UUID,
        organization_id: UUID,
        select_list_id: UUID,
        req: PipelineStageSelectListValueCreateRequest,
    ) -> PipelineStageSelectListValueMutateDto:
        """
        process one pipeline_stage_select_list_value item
        """

        # validate newly created pipeline stage select list
        await self._get_pipeline_stage_sl_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=select_list_id,
        )

        # fetch newly created pipeline stage select list from database by id
        # throws exception if not found
        pipeline_stage_select_list_metadata = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_sl_metadata_by_sl_id(
                select_list_id=select_list_id, organization_id=organization_id
            )
        )

        slv_dto = await self.select_list_service.add_select_list_value(
            select_list_id=select_list_id,
            actioning_user_id=actioning_user_id,
            organization_id=organization_id,
            slvcr=req.select_list_value_req,
        )

        slv_metadata = await self.pipeline_stage_sl_repo.insert(
            SelectListCommonLogic.map_LSSLCR_to_LSSLVM(
                req=req,
                select_list_value_id=slv_dto.primary_value.id,
                pipeline_stage_select_list_metadata_id=pipeline_stage_select_list_metadata.id,
                actioning_user_id=actioning_user_id,
                organization_id=organization_id,
            )
        )

        updated_sibling_slv_metadata = await self.pipeline_stage_sl_repo.map_all_pipeline_stage_slv_metadata_by_slv_ids(
            select_list_value_ids={lv.id for lv in slv_dto.updated_sibling_values},
            organization_id=organization_id,
        )

        updated_sibling_slv_dtos: list[PipelineStageSelectListValueDto] = [
            PipelineStageSelectListValueDto(
                select_list_value=lv,
                pipeline_stage_select_list_value_metadata=updated_sibling_slv_metadata[
                    lv.id
                ],
            )
            for lv in slv_dto.updated_sibling_values
        ]

        return PipelineStageSelectListValueMutateDto(
            pipeline_stage_select_list_value_metadata=slv_metadata,
            select_list_value=slv_dto.primary_value,
            updated_sibling_values=updated_sibling_slv_dtos,
        )

    async def activate_pipeline_stage_select_list(
        self,
        *,
        pipeline_stage_select_list_id: UUID,
        actioning_user_id: UUID,
        organization_id: UUID,
    ) -> PipelineStageSelectListMutateDto:
        current_stage_list_dto = (
            await self._get_pipeline_stage_sl_dto_and_validate_modifiable(
                organization_id=organization_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
            )
        )
        if current_stage_list_dto.select_list_status == SelectListStatus.ACTIVE:
            logger.warning(
                "Pipeline stage select list is already active.",
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                actioning_user_id=actioning_user_id,
            )
            return PipelineStageSelectListMutateDto(
                list_metadata=current_stage_list_dto.list_metadata,
                select_list=current_stage_list_dto.select_list,
                updated_stage_list_value_dtos=[],
            )
        activated_select_list = await self.select_list_service.activate_select_list(
            select_list_id=pipeline_stage_select_list_id,
            organization_id=organization_id,
            requesting_user_id=actioning_user_id,
        )
        return PipelineStageSelectListMutateDto(
            list_metadata=current_stage_list_dto.list_metadata,
            select_list=activated_select_list,
            updated_stage_list_value_dtos=[],
        )

    async def _get_pipeline_stage_sl_dto_and_validate_modifiable(
        self, *, organization_id: UUID, pipeline_stage_select_list_id: UUID
    ) -> PipelineStageSelectListDto:
        pipeline_sl_dto = (
            await self.pipeline_stage_sl_repo.map_all_pipeline_stage_sl_dto_by_sl_id(
                organization_id=organization_id,
                select_list_ids={pipeline_stage_select_list_id},
            )
        )[pipeline_stage_select_list_id]
        if not pipeline_sl_dto.select_list.status.is_user_modifiable():
            raise InvalidArgumentError(
                f"Pipeline stage select list is not modifiable. (list_id: {pipeline_stage_select_list_id})"
            )
        return pipeline_sl_dto

    async def bootstrap_organization_default_pipeline_stage_select_list(
        self, *, organization_id: UUID, actioning_user_id: UUID
    ) -> PipelineStageDto | None:
        """
        Bootstrap the default pipeline stage select list for the organization.
        If the organization already has any active pipeline stage select list, do nothing.
        """
        existing_pipeline_stage_sl_summaries = (
            await self.list_pipeline_stage_select_list_summary(
                organization_id=organization_id
            )
        )
        if any(
            pipeline_stage_sl_summary.status == SelectListStatus.ACTIVE
            for pipeline_stage_sl_summary in existing_pipeline_stage_sl_summaries
        ):
            logger.info(
                "Organization already has an active pipeline stage select list. "
                "Skipping bootstrap.",
                organization_id=organization_id,
            )
            return None
        created = await self.create_pipeline_stage_select_list(
            organization_id=organization_id,
            actioning_user_id=actioning_user_id,
            req=self._get_organization_default_pipeline_stage_select_list_bootstrap_request(
                organization_id=organization_id
            ),
        )
        logger.info(
            "Default pipeline stage select list created for organization.",
            organization_id=organization_id,
            pipeline_stage_select_list_id=created.select_list_id,
        )
        return created

    @classmethod
    def _get_organization_default_pipeline_stage_select_list_bootstrap_request(
        cls, *, organization_id: UUID
    ) -> PipelineStageSelectListCreateRequest:
        return PipelineStageSelectListCreateRequest(
            pipeline_process_display_name="Default Pipeline Process",
            is_default=True,
            display_name=temp_common_pipeline_funnel.display_name,
            description="Default pipeline stage select list",
            list_value_create_multi_req=PipelineStageSelectListValueCreateMultiRequest(
                value_reqs=tuple(
                    PipelineStageSelectListValueCreateRequest(
                        is_converted=_stage["is_converted"],
                        select_list_value_req=SelectListValueCreateRequest(
                            display_value=_stage["display_name"],
                            api_name=_stage["display_name"],
                            is_default=False,
                        ),
                    )
                    for _stage in temp_common_pipeline_funnel.v2_default_pipeline_stages
                )
            ),
        )

    async def get_pipeline_stage_select_list(
        self,
        *,
        select_list_id: UUID,  # select list id in select_list table
        organization_id: UUID,
        exclude_deleted: bool = True,
    ) -> PipelineStageDto:
        """
        get pipeline stage select list info from generic and pipeline specific from
        select list and select list value table by id
        """

        # get generic (select_list, select_list_value) from db
        sl_dto = await self.select_list_service.get_full_select_list(
            select_list_id=select_list_id,
            organization_id=organization_id,
            exclude_deleted=exclude_deleted,
        )

        # get pipeline specific pipeline_stage_select_list from db
        pipeline_stage_sl_metadata = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_sl_metadata_by_sl_id(
                select_list_id=select_list_id,
                organization_id=organization_id,
            )
        )

        # combine generic select_list and pipeline specific pipeline_stage_select_list
        pipeline_stage_sl_dto = PipelineStageSelectListDto(
            list_metadata=pipeline_stage_sl_metadata,
            select_list=sl_dto.select_list,
        )

        # get pipeline specific pipeline_stage_select_list_value from db
        pipeline_stage_slvs = await self.pipeline_stage_sl_repo.list_all_pipeline_stage_slv_metadata_by_sl_metadata_id(
            pipeline_stage_select_list_metadata_id=pipeline_stage_sl_metadata.id,
            organization_id=organization_id,
            # make sure we also get the deleted / deactivated values to render the full list
            only_active=False,
        )

        # combine generic select_list_value and pipeline specific pipeline_stage_select_list_value
        (
            match_pipeline_stage_values,
            unmatched_found,
        ) = SelectListCommonLogic.match_pipeline_stage_values(
            pipeline_stage_list=pipeline_stage_slvs,
            select_list_values=sl_dto.select_list_values,
        )

        if unmatched_found:
            logger.error(
                f"Unmatched pipeline stage select list values found for select_list {select_list_id}"
            )

        return PipelineStageDto(
            select_list_dto=pipeline_stage_sl_dto,
            select_list_value_dtos=tuple(match_pipeline_stage_values),
        )

    async def list_pipeline_stage_select_list_summary(
        self, *, organization_id: UUID
    ) -> list[PipelineStageSelectListSummary]:
        pipeline_stage_sl_dtos_by_sl_id = (
            await self.pipeline_stage_sl_repo.map_all_pipeline_stage_sl_dto_by_sl_id(
                organization_id=organization_id
            )
        )
        return sorted(
            [
                PipelineStageSelectListSummary.from_pipeline_stage_select_list_dto(v)
                for v in pipeline_stage_sl_dtos_by_sl_id.values()
            ],
            key=lambda x: x.created_at,
            reverse=True,
        )

    @log_context(from_kwargs=["organization_id"])
    async def find_organization_default_pipeline_stage_select_list(
        self, *, organization_id: UUID
    ) -> PipelineStageDto | None:
        summaries = await self.list_pipeline_stage_select_list_summary(
            organization_id=organization_id
        )
        active_summaries = [
            summary
            for summary in summaries
            if summary.status == SelectListStatus.ACTIVE
        ]
        if not active_summaries:
            logger.error(
                "No pipeline stage select list found for organization",
            )
            return None

        active_default_stage_summary = next(
            (summary for summary in active_summaries if summary.is_default),
            None,
        )
        if not active_default_stage_summary:
            logger.info(
                "No active default pipeline stage select list found for organization, using first active pipeline stage select list",
            )
            active_default_stage_summary = active_summaries[0]
        return await self.get_pipeline_stage_select_list(
            select_list_id=active_default_stage_summary.id,
            organization_id=organization_id,
        )

    @log_context(from_kwargs=["organization_id"])
    async def find_organization_default_pipeline_stage_select_list_first_converted_value(
        self, *, organization_id: UUID
    ) -> PipelineStageSelectListValueDto | None:
        pipeline_stage = (
            await self.find_organization_default_pipeline_stage_select_list(
                organization_id=organization_id,
            )
        )
        if not pipeline_stage:
            return None
        return pipeline_stage.first_converted_pipeline_stage_slv

    @log_context(from_kwargs=["organization_id"])
    async def find_organization_default_pipeline_stage_select_list_first_value(
        self, *, organization_id: UUID
    ) -> PipelineStageSelectListValueDto | None:
        pipeline_stage = (
            await self.find_organization_default_pipeline_stage_select_list(
                organization_id=organization_id,
            )
        )
        if not pipeline_stage:
            return None
        return pipeline_stage.first_active_pipeline_stage_slv

    async def list_pipeline_stage_select_list_dtos(
        self, *, organization_id: UUID
    ) -> list[PipelineStageDto]:
        summaries = await self.list_pipeline_stage_select_list_summary(
            organization_id=organization_id
        )
        return [
            await self.get_pipeline_stage_select_list(
                select_list_id=summary.id, organization_id=organization_id
            )
            for summary in summaries
        ]

    async def rerank_pipeline_stage_select_list_values(
        self,
        *,
        organization_id: UUID,
        actioning_user_id: UUID,
        select_list_id: UUID,
        rerank_multi_request: PipelineStageSelectListValueRerankMultiRequest,
    ) -> PipelineStageSelectListMutateDto:
        sl_dto = await self._get_pipeline_stage_sl_dto_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=select_list_id,
        )
        reranked_mutate_dtos: list[PipelineStageSelectListValueDto] = []

        # todo(xw): Add rerank sequence validation here to ensure they can be completed

        for _value_rerank_req in rerank_multi_request.ordered_rerank_reqs:
            _pre_update_slv_dto = (
                await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                    select_list_value_id=_value_rerank_req.select_list_value_id,
                    organization_id=organization_id,
                )
            )
            _reranked_primary_slv = (
                await self.select_list_service.re_rank_select_list_value(
                    select_list_value_id=_value_rerank_req.select_list_value_id,
                    organization_id=organization_id,
                    actioning_user_id=actioning_user_id,
                    slvrr=_value_rerank_req.rank_location,
                )
            )
            reranked_mutate_dtos.append(
                PipelineStageSelectListValueDto(
                    pipeline_stage_select_list_value_metadata=_pre_update_slv_dto.pipeline_stage_select_list_value_metadata,
                    select_list_value=_reranked_primary_slv,
                )
            )
        return PipelineStageSelectListMutateDto(
            list_metadata=sl_dto.list_metadata,
            select_list=sl_dto.select_list,
            updated_stage_list_value_dtos=reranked_mutate_dtos,
        )

    async def put_full_pipeline_stage_select_list(
        self,
        *,
        organization_id: UUID,
        actioning_user_id: UUID,
        select_list_id: UUID,
        req: PipelineStageSelectListValueFullPutRequest,
    ) -> PipelineStageDto:
        return await self.pipeline_stage_sl_repo.put_full_pipeline_stage_slv(
            organization_id=organization_id,
            actioning_user_id=actioning_user_id,
            select_list_id=select_list_id,
            req=req,
        )

    async def patch_pipeline_stage_select_list(
        self,
        pipeline_stage_select_list_id: UUID,
        req: PipelineStageSelectListPatchRequest,
        organization_id: UUID,
        user_id: UUID,
    ) -> PipelineStageSelectListMutateDto:
        pre_update_pipeline_stage_sl_dto = (
            await self._get_pipeline_stage_sl_dto_and_validate_modifiable(
                organization_id=organization_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
            )
        )
        stage_list_metadata = pre_update_pipeline_stage_sl_dto.list_metadata
        stage_list = pre_update_pipeline_stage_sl_dto.select_list

        # 1. Update select list level attrs
        if patch_select_list_req := req.to_select_list_patch_req():
            stage_list = await self.select_list_service.update_select_list(
                select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                actioning_user_id=user_id,
                slpr=patch_select_list_req,
            )

        # 2. Update pipeline stage select list default flag
        if (
            specified(req.set_as_default)
            and stage_list_metadata.is_default != req.set_as_default
        ):
            if req.set_as_default:
                stage_list_metadata = (
                    await self.pipeline_stage_sl_repo.set_pipeline_stage_sl_default(
                        pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                        organization_id=organization_id,
                        requesting_user_id=user_id,
                    )
                )
            else:
                assert_never(req.set_as_default)
        # 3. Update pipeline stage select list attrs
        if specified(req.pipeline_process_display_name):
            stage_list_metadata = await self.pipeline_stage_sl_repo.update_pipeline_stage_sl_metadata_by_sl_id(
                select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                update=PipelineStageSelectListMetadataUpdate(
                    pipeline_process_display_name=req.pipeline_process_display_name,
                    updated_by_user_id=user_id,
                ),
            )

        # 4. Update pipeline stage select list values
        updated_pipeline_stage_value_dto_by_value_id: dict[
            UUID, PipelineStageSelectListValueDto
        ] = {}
        for (
            select_list_value_id,
            value_patch_req,
        ) in req.value_patch_req_by_value_id.items():
            _stage_list_value_mutate_dto = (
                await self.patch_pipeline_stage_select_list_value(
                    actioning_user_id=user_id,
                    organization_id=organization_id,
                    select_list_id=pipeline_stage_select_list_id,
                    select_list_value_id=select_list_value_id,
                    req=value_patch_req,
                )
            )
            updated_pipeline_stage_value_dto_by_value_id[
                _stage_list_value_mutate_dto.select_list_value.id
            ] = _stage_list_value_mutate_dto
            for sibling in _stage_list_value_mutate_dto.updated_sibling_values:
                updated_pipeline_stage_value_dto_by_value_id[
                    sibling.select_list_value.id
                ] = sibling

        return PipelineStageSelectListMutateDto(
            list_metadata=stage_list_metadata,
            select_list=stage_list,
            updated_stage_list_value_dtos=list(
                updated_pipeline_stage_value_dto_by_value_id.values()
            ),
        )

    async def patch_pipeline_stage_select_list_value(
        self,
        *,
        actioning_user_id: UUID,
        organization_id: UUID,
        select_list_id: UUID,
        select_list_value_id: UUID,
        req: PipelineStageSelectListValuePatchRequest,
    ) -> PipelineStageSelectListValueMutateDto:
        await self._get_pipeline_stage_sl_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=select_list_id,
        )

        pre_update_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=select_list_value_id,
                organization_id=organization_id,
            )
        )

        updated_primary_value_metadata = (
            pre_update_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata
        )
        updated_primary_value = pre_update_pipeline_stage_slv_dto.select_list_value

        updated_sibling_values: list[SelectListValue] = []

        if req.select_list_value_patch_req:
            select_list_value_mutate_dto = (
                await self.select_list_service.patch_select_list_value(
                    select_list_id=select_list_id,
                    select_list_value_id=select_list_value_id,
                    organization_id=organization_id,
                    actioning_user_id=actioning_user_id,
                    slvpr=req.select_list_value_patch_req,
                )
            )
            updated_primary_value = select_list_value_mutate_dto.primary_value
            updated_sibling_values = select_list_value_mutate_dto.updated_sibling_values

        updated_sibling_value_metadata_by_value_id = await self.pipeline_stage_sl_repo.map_all_pipeline_stage_slv_metadata_by_slv_ids(
            select_list_value_ids={sv.id for sv in updated_sibling_values},
            organization_id=organization_id,
        )

        return PipelineStageSelectListValueMutateDto(
            pipeline_stage_select_list_value_metadata=updated_primary_value_metadata,
            select_list_value=updated_primary_value,
            updated_sibling_values=[
                PipelineStageSelectListValueDto(
                    pipeline_stage_select_list_value_metadata=updated_sibling_value_metadata_by_value_id[
                        sv.id
                    ],
                    select_list_value=sv,
                )
                for sv in updated_sibling_values
            ],
        )

    async def deactivate_pipeline_stage_select_list(
        self,
        *,
        pipeline_stage_select_list_id: UUID,
        actioning_user_id: UUID,
        organization_id: UUID,
    ) -> PipelineStageSelectListMutateDto:
        current_stage_list_dto = (
            await self._get_pipeline_stage_sl_dto_and_validate_modifiable(
                organization_id=organization_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
            )
        )

        if current_stage_list_dto.select_list_status == SelectListStatus.INACTIVE:
            logger.warning(
                "Pipeline stage select list is already inactive.",
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                actioning_user_id=actioning_user_id,
            )
            return PipelineStageSelectListMutateDto(
                list_metadata=current_stage_list_dto.list_metadata,
                select_list=current_stage_list_dto.select_list,
                updated_stage_list_value_dtos=[],
            )

        if current_stage_list_dto.list_metadata.is_default:
            raise InvalidArgumentError(
                "Cannot deactivate default pipeline stage select list "
                f"({current_stage_list_dto.list_metadata.id}). Set another list as "
                f"default first."
            )

        pipeline_stage_select_lists = await self.select_list_service.list_select_lists(
            organization_id=organization_id,
            standard_select_list_id=StdSelectListIdentifier.pipeline_stage,
            exclude_deleted=True,
        )
        active_pipeline_stage_select_list = [
            dssl
            for dssl in pipeline_stage_select_lists
            if dssl.status == SelectListStatus.ACTIVE
        ]
        if (
            len(active_pipeline_stage_select_list) == 1
            and active_pipeline_stage_select_list[0].id == pipeline_stage_select_list_id
        ):
            raise InvalidArgumentError(
                "Cannot deactivate the only active pipeline stage select list for "
                f"organization. (list_id: {pipeline_stage_select_list_id}, org_id: {organization_id})"
            )

        deactivated_select_list = (
            await self.pipeline_stage_sl_repo.deactivate_pipeline_stage_select_list(
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                requesting_user_id=actioning_user_id,
            )
        )
        return PipelineStageSelectListMutateDto(
            list_metadata=current_stage_list_dto.list_metadata,
            select_list=deactivated_select_list,
            updated_stage_list_value_dtos=[],
        )

    async def activate_pipeline_stage_select_list_value(
        self,
        *,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID,
        actioning_user_id: UUID,
        organization_id: UUID,
    ) -> PipelineStageSelectListValueMutateDto:
        await self._get_pipeline_stage_sl_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=pipeline_stage_select_list_id,
        )
        current_stage_list_value_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=pipeline_stage_select_list_value_id,
                organization_id=organization_id,
            )
        )
        if (
            current_stage_list_value_dto.select_list_value.status
            == SelectListValueStatus.ACTIVE
        ):
            logger.warning(
                "Pipeline stage select list value is already active.",
                pipeline_stage_select_list_value_id=pipeline_stage_select_list_value_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                actioning_user_id=actioning_user_id,
            )
            return PipelineStageSelectListValueMutateDto(
                pipeline_stage_select_list_value_metadata=current_stage_list_value_dto.pipeline_stage_select_list_value_metadata,
                select_list_value=current_stage_list_value_dto.select_list_value,
                updated_sibling_values=[],
            )
        activated_select_list_value = (
            await self.select_list_service.activate_select_list_value(
                select_list_value_id=pipeline_stage_select_list_value_id,
                organization_id=organization_id,
                requesting_user_id=actioning_user_id,
            )
        )
        return PipelineStageSelectListValueMutateDto(
            pipeline_stage_select_list_value_metadata=current_stage_list_value_dto.pipeline_stage_select_list_value_metadata,
            select_list_value=activated_select_list_value,
            updated_sibling_values=[],
        )

    async def deactivate_pipeline_stage_select_list_value(
        self,
        *,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID,
        user_id: UUID,
        organization_id: UUID,
    ) -> PipelineStageSelectListValueMutateDto:
        await self._get_pipeline_stage_sl_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=pipeline_stage_select_list_id,
        )
        current_stage_list_value_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=pipeline_stage_select_list_value_id,
                organization_id=organization_id,
            )
        )
        if (
            current_stage_list_value_dto.select_list_value.status
            == SelectListValueStatus.INACTIVE
        ):
            logger.warning(
                "Pipeline stage select list value is already inactive.",
                pipeline_stage_select_list_value_id=pipeline_stage_select_list_value_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                organization_id=organization_id,
                actioning_user_id=user_id,
            )
            return PipelineStageSelectListValueMutateDto(
                pipeline_stage_select_list_value_metadata=current_stage_list_value_dto.pipeline_stage_select_list_value_metadata,
                select_list_value=current_stage_list_value_dto.select_list_value,
                updated_sibling_values=[],
            )

        if current_stage_list_value_dto.pipeline_stage_select_list_value_metadata.is_converted:
            active_converted_pipeline_stage_slv_metadata_by_id = await self.pipeline_stage_sl_repo.map_pipeline_stage_slv_by_sl_metadata_id_group_by_id(
                pipeline_stage_select_list_metadata_id=current_stage_list_value_dto.pipeline_stage_select_list_value_metadata.pipeline_stage_select_list_metadata_id,
                organization_id=organization_id,
                only_active=True,
                only_converted=True,
            )
            if (
                len(active_converted_pipeline_stage_slv_metadata_by_id) == 1
                and current_stage_list_value_dto.pipeline_stage_select_list_value_metadata.id
                in active_converted_pipeline_stage_slv_metadata_by_id
            ):
                logger.warning(
                    "Cannot deactivate the only active converted pipeline stage select list value.",
                    pipeline_stage_select_list_value_id=pipeline_stage_select_list_value_id,
                    pipeline_stage_select_list_id=pipeline_stage_select_list_id,
                    organization_id=organization_id,
                    actioning_user_id=user_id,
                )
                raise InvalidArgumentError(
                    "Cannot deactivate the only active converted pipeline stage select list value."
                )

        deactivated_select_list_value = (
            await self.select_list_service.deactivate_select_list_value(
                select_list_value_id=pipeline_stage_select_list_value_id,
                organization_id=organization_id,
                requesting_user_id=user_id,
            )
        )
        return PipelineStageSelectListValueMutateDto(
            pipeline_stage_select_list_value_metadata=current_stage_list_value_dto.pipeline_stage_select_list_value_metadata,
            select_list_value=deactivated_select_list_value,
            updated_sibling_values=[],
        )

    @log_context(
        from_kwargs=[
            "organization_id",
            "pipeline_stage_select_list_id",
            "pipeline_stage_select_list_value_id",
            "requires_active",
        ]
    )
    async def validate_reference_and_get_effective_pipeline_stage_value(
        self,
        organization_id: UUID,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID,
        requires_active: bool,
    ) -> PipelineStageSelectListValueDto:
        if not (
            pipeline_stage_dto := await self.get_pipeline_stage_select_list(
                organization_id=organization_id,
                select_list_id=pipeline_stage_select_list_id,
                exclude_deleted=requires_active,
            )
        ):
            logger.error(
                "Pipeline stage select list not found.",
            )
            raise InvalidArgumentError(
                f"Pipeline stage select list not found. (list_id: {pipeline_stage_select_list_id})"
            )

        if requires_active and (
            pipeline_stage_dto.select_list_status != SelectListStatus.ACTIVE
        ):
            logger.error(
                "Pipeline stage select list is not active.",
            )
            raise InvalidArgumentError(
                f"Pipeline stage select list is not active. (list_id: {pipeline_stage_select_list_id})"
            )

        if not (
            effective_pipeline_stage_value
            := pipeline_stage_dto.get_effective_pipeline_stage_value(
                pipeline_stage_slv_id=pipeline_stage_select_list_value_id,
            )
        ):
            logger.error(
                "Pipeline stage select list value not found.",
            )
            raise InvalidArgumentError(
                f"Pipeline stage select list value not found. (value_id: {pipeline_stage_select_list_value_id})"
            )
        if requires_active and (
            effective_pipeline_stage_value.select_list_value_status
            != SelectListValueStatus.ACTIVE
        ):
            logger.error(
                "Pipeline stage select list value is not active.",
            )
            raise InvalidArgumentError(
                f"Pipeline stage select list value is not active. (value_id: {pipeline_stage_select_list_value_id})"
            )
        return effective_pipeline_stage_value

    async def _validate_pipeline_stage_select_list_value_remap(
        self,
        *,
        organization_id: UUID,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID,
        map_to_pipeline_stage_select_list_value_id: UUID,
    ) -> None:
        if (
            map_to_pipeline_stage_select_list_value_id
            == pipeline_stage_select_list_value_id
        ):
            raise InvalidArgumentError(
                "Cannot map a pipeline stage list value to itself. "
                f"pipeline_stage_list_value_id: {pipeline_stage_select_list_value_id}"
            )
        await self._get_pipeline_stage_sl_and_validate_modifiable(
            organization_id=organization_id,
            pipeline_stage_select_list_id=pipeline_stage_select_list_id,
        )

        pre_map_src_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=pipeline_stage_select_list_value_id,
                organization_id=organization_id,
            )
        )
        pre_map_src_metadata = (
            pre_map_src_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata
        )
        pre_map_dst_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=map_to_pipeline_stage_select_list_value_id,
                organization_id=organization_id,
            )
        )
        pre_map_dst_metadata = (
            pre_map_dst_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata
        )
        if pre_map_src_metadata.is_converted != pre_map_dst_metadata.is_converted:
            logger.warning(
                "Cannot map a pipeline stage list value to a value with different 'is_converted' flag.",
                src_value_id=pipeline_stage_select_list_value_id,
                dst_value_id=map_to_pipeline_stage_select_list_value_id,
                src_is_converted=pre_map_src_metadata.is_converted,
                dst_is_converted=pre_map_dst_metadata.is_converted,
            )
            raise InvalidArgumentError(
                "Cannot map a pipeline stage list value to a value with different 'is_converted' flag."
            )

    async def remove_and_remap_pipeline_stage_select_list_value(
        self,
        *,
        actioning_user_id: UUID,
        organization_id: UUID,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID,
        map_to_pipeline_stage_select_list_value_id: UUID,
    ) -> PipelineStageSelectListValueReMapDto:
        await self._validate_pipeline_stage_select_list_value_remap(
            organization_id=organization_id,
            pipeline_stage_select_list_id=pipeline_stage_select_list_id,
            pipeline_stage_select_list_value_id=pipeline_stage_select_list_value_id,
            map_to_pipeline_stage_select_list_value_id=map_to_pipeline_stage_select_list_value_id,
        )

        mapped_select_list_pair = (
            await self.select_list_service.soft_map_and_mark_select_list_value_deleted(
                organization_id=organization_id,
                select_list_id=pipeline_stage_select_list_id,
                src_select_list_value_id=pipeline_stage_select_list_value_id,
                dst_select_list_value_id=map_to_pipeline_stage_select_list_value_id,
                requesting_user_id=actioning_user_id,
            )
        )
        src_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=mapped_select_list_pair.src_select_list_value.id,
                organization_id=organization_id,
                exclude_deleted=False,
                # at this time, we need to get the deleted src value
            )
        )
        dst_pipeline_stage_slv_dto = (
            await self.pipeline_stage_sl_repo.get_pipeline_stage_slv_dto_by_slv_id(
                select_list_value_id=not_none(
                    mapped_select_list_pair.dst_select_list_value
                ).id,
                organization_id=organization_id,
            )
        )
        return PipelineStageSelectListValueReMapDto(
            removed_value=PipelineStageSelectListValueDto(
                pipeline_stage_select_list_value_metadata=src_pipeline_stage_slv_dto.pipeline_stage_select_list_value_metadata,
                select_list_value=mapped_select_list_pair.src_select_list_value,
            ),
            mapped_to_value=dst_pipeline_stage_slv_dto,
        )

    async def validate_pipeline_stage_reference(
        self,
        organization_id: UUID,
        pipeline_stage_select_list_id: UUID,
        pipeline_stage_select_list_value_id: UUID | None = None,
    ) -> None:
        """
        Validate that the provided pipeline stage select list and (optionally) the
        pipeline stage select list value are active and can be referenced in pipeline records
        within the same organization.
        Otherwise, raise InvalidArgumentError.
        """
        pipeline_stage_select_list = (
            await self._get_pipeline_stage_sl_and_validate_modifiable(
                organization_id=organization_id,
                pipeline_stage_select_list_id=pipeline_stage_select_list_id,
            )
        )
        if pipeline_stage_select_list.status != SelectListStatus.ACTIVE:
            raise InvalidArgumentError(
                f"Pipeline stage select list is not active. (list_id: {pipeline_stage_select_list_id})"
            )

        if pipeline_stage_select_list_value_id:
            pipeline_stage_select_list_value = (
                await self.select_list_service.get_select_list_value(
                    select_list_value_id=pipeline_stage_select_list_value_id,
                    select_list_id=pipeline_stage_select_list_id,
                    organization_id=organization_id,
                )
            )
            if pipeline_stage_select_list_value.status != SelectListValueStatus.ACTIVE:
                raise InvalidArgumentError(
                    f"Pipeline stage select list value is not active. (value_id: {pipeline_stage_select_list_value_id})"
                )


def get_pipeline_stage_select_list_service(
    *,
    engine: DatabaseEngine,
) -> PipelineStageSelectListService:
    return PipelineStageSelectListService(
        pipeline_stage_select_list_repository=PipelineStageSelectListRepository(
            engine=engine
        ),
        select_list_service=get_select_list_service(engine=engine),
    )
