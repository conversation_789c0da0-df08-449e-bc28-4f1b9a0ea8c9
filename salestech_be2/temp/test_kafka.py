import fire
import aiokafka


async def producer2():
    from kafka import KafkaProducer
    producer = KafkaProducer(bootstrap_servers='localhost:9092')
    producer.send('my_favorite_topic', b'some_message_bytes')
    producer.flush()


async def producer():
    producer = aiokafka.AIOKafkaProducer(bootstrap_servers="localhost:9092")
    await producer.start()
    try:
        await producer.send_and_wait("my_topic", b"Super message")
    finally:
        await producer.stop()


async def consumer2():
    from kafka import KafkaConsumer
    consumer = KafkaConsumer('my_favorite_topic')
    for msg in consumer:
        print(msg)


async def consumer():
    from aiokafka.helpers import create_ssl_context
    from salestech_be.integrations.kafka.types import SecurityProtocol
    from salestech_be.integrations.kafka.types import SASLMechanism
    from salestech_be.integrations.kafka.types import AutoOffsetReset
    # consumer = aiokafka.AIOKafkaConsumer(
    #     "my_topic",
    #     bootstrap_servers='localhost:9092',
    #     security_protocol=SecurityProtocol.SASL_SSL,
    #     sasl_mechanism=SASLMechanism.SCRAM_SHA_512,
    #     sasl_plain_username="user",
    #     sasl_plain_password="password2",
    #     ssl_context=create_ssl_context(),
    #     group_id="my-group",
    #     auto_offset_reset=AutoOffsetReset.EARLIEST,
    #     enable_auto_commit=True,
    # )
    consumer = aiokafka.AIOKafkaConsumer(
        "my_topic",
        bootstrap_servers='localhost:9092',
    )
    await consumer.start()
    try:
        async for msg in consumer:
            print(
                "{}:{:d}:{:d}: key={} value={} timestamp_ms={}".format(
                    msg.topic, msg.partition, msg.offset, msg.key, msg.value,
                    msg.timestamp)
            )
    finally:
        await consumer.stop()


if __name__ == "__main__":
    fire.Fire()
