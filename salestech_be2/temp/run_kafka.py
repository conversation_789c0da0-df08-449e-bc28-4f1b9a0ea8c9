import asyncio
import datetime
import uuid
import fire
from uuid import UUID
import aiokafka
from pydantic import BaseModel

TOPIC = 'my_topic'
TOPIC = 'dev_cdc_v2'

class TestMsg(BaseModel):
    name: str


async def producer() -> None:
    producer = aiokafka.AIOKafkaProducer(
        bootstrap_servers="localhost:9092"
    )
    await producer.start()
    try:
        msg = TestMsg(name="test")
        await producer.send_and_wait("my_topic", msg.model_dump_json().encode("utf8"))
    finally:
        await producer.stop()


async def consumer() -> None:
    consumer = aiokafka.AIOKafkaConsumer(
        TOPIC,
        bootstrap_servers="localhost:9092",
        group_id="default",
    )
    await consumer.start()
    try:
        async for msg in consumer:
            print(
                f"{msg.topic}:{msg.partition:d}:{msg.offset:d}: key={msg.key} value={msg.value} timestamp_ms={msg.timestamp}"
            )
    finally:
        await consumer.stop()


async def consumer2() -> None:
    from salestech_be.integrations.kafka.kafka_manager import MSKConsumerFactory
    consumer = MSKConsumerFactory.create(TOPIC, group_id="default", model_class=TestMsg)
    async for message in consumer.events():
        print(message.payload)


if __name__ == "__main__":
    fire.Fire()
