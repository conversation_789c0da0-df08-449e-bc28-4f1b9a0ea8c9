import asyncio
import datetime
import time
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from starlette.status import HTTP_504_GATEWAY_TIMEOUT

REQUEST_TIMEOUT_ERROR = 1  # Threshold

app = FastAPI()


@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/sleep")
async def route_for_test(sleep_time: float) -> None:
    print("Before sleep")
    await asyncio.sleep(sleep_time)
    print("After sleep")
    await asyncio.sleep(0.01)
    print("After second sleep")
    return {"message": sleep_time, "time": datetime.datetime.now()}

@app.middleware("http")
async def timeout_middleware(request: Request, call_next):
    try:
        start_time = time.time()
        return await asyncio.wait_for(call_next(request), timeout=REQUEST_TIMEOUT_ERROR)

    except asyncio.TimeoutError:
        process_time = time.time() - start_time
        return JSONResponse(
            {
                'detail': 'Request processing time excedeed limit',
                'processing_time': process_time
            },
            status_code=HTTP_504_GATEWAY_TIMEOUT
        )
