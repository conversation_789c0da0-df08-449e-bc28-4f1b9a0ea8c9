from typing import Union
import dbt_connector_patch

from fastapi import FastAPI


from dbt_metricflow.cli.dbt_connectors.adapter_backed_client import SupportedAdapterTypes

print(SupportedAdapterTypes.MATERIALIZE.value)
quit()
app = FastAPI()


@app.get("/")
def read_root():
    return {"Hello": "World"}


@app.get("/items/{item_id}")
def read_item(item_id: int, q: Union[str, None] = None):
    return {"item_id": item_id, "q": q}
