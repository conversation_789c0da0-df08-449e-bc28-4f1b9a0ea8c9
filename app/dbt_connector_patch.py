from __future__ import annotations

import enum
from dbt_semantic_interfaces.enum_extension import assert_values_exhausted
from metricflow.protocols.sql_client import SqlEngine
from metricflow.sql.render.big_query import BigQuerySqlPlanRenderer
from metricflow.sql.render.databricks import DatabricksSqlPlanRenderer
from metricflow.sql.render.duckdb_renderer import DuckDbSqlPlanRenderer
from metricflow.sql.render.postgres import PostgresSQLSqlPlanRenderer
from metricflow.sql.render.redshift import RedshiftSqlPlanRenderer
from metricflow.sql.render.snowflake import SnowflakeSqlPlanRenderer
from metricflow.sql.render.sql_plan_renderer import SqlPlanRenderer
from metricflow.sql.render.trino import TrinoSqlPlanRenderer
from dbt_metricflow.cli.dbt_connectors import adapter_backed_client


class SupportedAdapterTypes(enum.Enum):
    """Enumeration of supported dbt adapter types."""

    DATABRICKS = "databricks"
    POSTGRES = "postgres"
    MATERIALIZE = "materialize"  # Add Materialize support
    SNOWFLAKE = "snowflake"
    REDSHIFT = "redshift"
    BIGQUERY = "bigquery"
    DUCKDB = "duckdb"
    TRINO = "trino"

    @property
    def sql_engine_type(self) -> SqlEngine:
        """Return the SqlEngine corresponding to the supported adapter type."""
        if self is SupportedAdapterTypes.BIGQUERY:
            return SqlEngine.BIGQUERY
        elif self is SupportedAdapterTypes.DATABRICKS:
            return SqlEngine.DATABRICKS
        elif self is SupportedAdapterTypes.POSTGRES:
            return SqlEngine.POSTGRES
        elif self is SupportedAdapterTypes.MATERIALIZE:
            return SqlEngine.POSTGRES  # Treat Materialize as Postgres-compatible
        elif self is SupportedAdapterTypes.REDSHIFT:
            return SqlEngine.REDSHIFT
        elif self is SupportedAdapterTypes.SNOWFLAKE:
            return SqlEngine.SNOWFLAKE
        elif self is SupportedAdapterTypes.DUCKDB:
            return SqlEngine.DUCKDB
        elif self is SupportedAdapterTypes.TRINO:
            return SqlEngine.TRINO
        else:
            assert_values_exhausted(self)

    @property
    def sql_plan_renderer(self) -> SqlPlanRenderer:
        """Return the SqlPlanRenderer corresponding to the supported adapter type."""
        if self is SupportedAdapterTypes.BIGQUERY:
            return BigQuerySqlPlanRenderer()
        elif self is SupportedAdapterTypes.DATABRICKS:
            return DatabricksSqlPlanRenderer()
        elif self is SupportedAdapterTypes.POSTGRES:
            return PostgresSQLSqlPlanRenderer()
        elif self is SupportedAdapterTypes.MATERIALIZE:
            return PostgresSQLSqlPlanRenderer()  # Use Postgres SQL renderer for Materialize
        elif self is SupportedAdapterTypes.REDSHIFT:
            return RedshiftSqlPlanRenderer()
        elif self is SupportedAdapterTypes.SNOWFLAKE:
            return SnowflakeSqlPlanRenderer()
        elif self is SupportedAdapterTypes.DUCKDB:
            return DuckDbSqlPlanRenderer()
        elif self is SupportedAdapterTypes.TRINO:
            return TrinoSqlPlanRenderer()
        else:
            assert_values_exhausted(self)


adapter_backed_client.SupportedAdapterTypes = SupportedAdapterTypes
