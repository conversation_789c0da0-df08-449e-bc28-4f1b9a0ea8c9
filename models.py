from tortoise.models import Model
from tortoise import fields

class Pipeline(Model):
    id = fields.UUIDField(primary_key=True)
    display_name = fields.TextField()
    created_at = fields.DatetimeField(auto_now_add=True)
    updated_at = fields.DatetimeField(auto_now=True)


class PipelineTracking(Model):
    # Defining `id` field is optional, it will be defined automatically
    # if you haven't done it yourself
    id = fields.UUIDField(primary_key=True)
    field_name = fields.TextField()
    from_value_uuid = fields.UUIDField()
    to_value_uuid = fields.UUIDField()
    created_at = fields.DatetimeField(auto_now_add=True)
    pipeline = fields.ForeignKeyField('models.Pipeline', related_name='pipeline_trackings')

    # Defining ``__str__`` is also optional, but gives you pretty
    # represent of model in debugger and interpreter
    def __str__(self):
        return str(self.pipeline_id)

    class Meta:
        table = "pipeline_tracking"
