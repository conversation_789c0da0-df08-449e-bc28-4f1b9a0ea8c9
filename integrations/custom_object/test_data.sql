-- 插入示例数据
INSERT INTO custom_object_definitions 
(name, label, description, tenant_id) VALUES
('customer', 'Customer', 'Customer information', 1),
('contact', 'Contact', 'Contact information', 1),
('deal', 'Deal', 'Deal information', 1);

-- 插入属性定义
INSERT INTO property_definitions 
(object_definition_id, name, label, type, required, constraints) VALUES
(1, 'company_name', 'Company Name', 'string', true, '{"max_length": 100}'),
(1, 'industry', 'Industry', 'string', true, '{}'),
(1, 'annual_revenue', 'Annual Revenue', 'number', false, '{"min": 0}'),
(2, 'first_name', 'First Name', 'string', true, '{"max_length": 50}'),
(2, 'last_name', 'Last Name', 'string', true, '{"max_length": 50}'),
(2, 'email', 'Email', 'string', true, '{"format": "email"}'),
(3, 'amount', 'Deal Amount', 'number', true, '{"min": 0}'),
(3, 'stage', 'Deal Stage', 'string', true, '{}'),
(3, 'close_date', 'Expected Close Date', 'string', true, '{}');

-- 插入示例对象数据
INSERT INTO dynamic_objects 
(definition_id, tenant_id, data) VALUES
(1, 1, '{"company_name": "Acme Corp", "industry": "Technology", "annual_revenue": 1000000}'),
(1, 1, '{"company_name": "Beta Industries", "industry": "Manufacturing", "annual_revenue": 5000000}'),
(2, 1, '{"first_name": "John", "last_name": "Doe", "email": "<EMAIL>"}'),
(2, 1, '{"first_name": "Jane", "last_name": "Smith", "email": "<EMAIL>"}'),
(3, 1, '{"amount": 50000, "stage": "Proposal", "close_date": "2024-06-30"}');

-- 插入关系数据
INSERT INTO object_relationships 
(source_object_id, target_object_id, relationship_type) VALUES
(1, 3, 'has_deal'),
(1, 3, 'has_contact'),
(2, 4, 'has_contact');