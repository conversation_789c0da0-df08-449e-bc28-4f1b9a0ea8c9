import json
from datetime import datetime
from typing import Dict, List, Optional
import psycopg2
from psycopg2.extras import RealDictCursor
import redis

class CustomObjectManager:
    def __init__(self):
        # 数据库连接配置
        self.db_config = {
            "dbname": "custom_objects_db",
            "user": "postgres",
            "password": "your_password",
            "host": "localhost",
            "port": "5432"
        }
        # Redis配置
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)

    def get_db_connection(self):
        return psycopg2.connect(**self.db_config)

    def create_object_definition(self, name: str, label: str, description: str, 
                               tenant_id: int) -> int:
        with self.get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO custom_object_definitions 
                    (name, label, description, tenant_id)
                    VALUES (%s, %s, %s, %s)
                    RETURNING id
                """, (name, label, description, tenant_id))
                definition_id = cur.fetchone()[0]
                conn.commit()
                return definition_id

    def add_property_definition(self, object_definition_id: int, name: str, 
                              label: str, type: str, required: bool = False,
                              default_value: str = None, 
                              constraints: Dict = None) -> int:
        with self.get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO property_definitions 
                    (object_definition_id, name, label, type, required, 
                     default_value, constraints)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                """, (object_definition_id, name, label, type, required,
                     default_value, json.dumps(constraints or {})))
                property_id = cur.fetchone()[0]
                conn.commit()
                return property_id

    def create_object(self, definition_id: int, tenant_id: int, 
                     data: Dict) -> int:
        # 验证数据
        if not self._validate_object_data(definition_id, data):
            raise ValueError("Invalid object data")

        with self.get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO dynamic_objects 
                    (definition_id, tenant_id, data)
                    VALUES (%s, %s, %s)
                    RETURNING id
                """, (definition_id, tenant_id, json.dumps(data)))
                object_id = cur.fetchone()[0]
                conn.commit()
                return object_id

    def _validate_object_data(self, definition_id: int, data: Dict) -> bool:
        with self.get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute("""
                    SELECT * FROM property_definitions 
                    WHERE object_definition_id = %s
                """, (definition_id,))
                properties = cur.fetchall()

                for prop in properties:
                    if prop['required'] and prop['name'] not in data:
                        return False
                    if prop['name'] in data:
                        if not self._validate_property_value(
                            data[prop['name']], 
                            prop['type'], 
                            prop['constraints']
                        ):
                            return False
                return True

    def _validate_property_value(self, value, type: str, 
                               constraints: Dict) -> bool:
        if constraints is None:
            constraints = {}

        if type == 'string':
            if not isinstance(value, str):
                return False
            max_length = constraints.get('max_length')
            if max_length and len(value) > max_length:
                return False
        elif type == 'number':
            if not isinstance(value, (int, float)):
                return False
            min_value = constraints.get('min')
            max_value = constraints.get('max')
            if min_value is not None and value < min_value:
                return False
            if max_value is not None and value > max_value:
                return False
        elif type == 'boolean':
            if not isinstance(value, bool):
                return False
        return True

    def query_objects(self, definition_id: int, filters: Dict = None) -> List[Dict]:
        query = """
            SELECT * FROM dynamic_objects 
            WHERE definition_id = %s
        """
        params = [definition_id]

        if filters:
            for key, value in filters.items():
                query += f" AND data->>{key} = %s"
                params.append(value)

        with self.get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cur:
                cur.execute(query, tuple(params))
                return cur.fetchall()

    def create_relationship(self, source_id: int, target_id: int, 
                          relationship_type: str) -> int:
        with self.get_db_connection() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    INSERT INTO object_relationships 
                    (source_object_id, target_object_id, relationship_type)
                    VALUES (%s, %s, %s)
                    RETURNING id
                """, (source_id, target_id, relationship_type))
                relationship_id = cur.fetchone()[0]
                conn.commit()
                return relationship_id