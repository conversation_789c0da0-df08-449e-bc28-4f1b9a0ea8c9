-- 创建数据库表
CREATE TABLE custom_object_definitions (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    label VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id INTEGER NOT NULL,
    status VARCHAR(50) DEFAULT 'active'
);

CREATE TABLE property_definitions (
    id SERIAL PRIMARY KEY,
    object_definition_id INTEGER REFERENCES custom_object_definitions(id),
    name VARCHAR(255) NOT NULL,
    label VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    required BOOLEAN DEFAULT false,
    default_value TEXT,
    constraints JSONB
);

CREATE TABLE dynamic_objects (
    id SERIAL PRIMARY KEY,
    definition_id INTEGER REFERENCES custom_object_definitions(id),
    tenant_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    data JSONB NOT NULL
);

CREATE TABLE object_relationships (
    id SERIAL PRIMARY KEY,
    source_object_id INTEGER REFERENCES dynamic_objects(id),
    target_object_id INTEGER REFERENCES dynamic_objects(id),
    relationship_type VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);