def main():
    manager = CustomObjectManager()

    # 1. 创建项目对象定义
    project_def_id = manager.create_object_definition(
        name="project",
        label="Project",
        description="Customer project information",
        tenant_id=1
    )

    # 2. 添加项目属性定义
    manager.add_property_definition(
        project_def_id,
        name="name",
        label="Project Name",
        type="string",
        required=True,
        constraints={"max_length": 100}
    )

    manager.add_property_definition(
        project_def_id,
        name="budget",
        label="Project Budget",
        type="number",
        required=True,
        constraints={"min": 0}
    )

    manager.add_property_definition(
        project_def_id,
        name="start_date",
        label="Start Date",
        type="string",
        required=True
    )

    # 3. 创建任务对象定义
    task_def_id = manager.create_object_definition(
        name="task",
        label="Task",
        description="Project task information",
        tenant_id=1
    )

    # 4. 添加任务属性定义
    manager.add_property_definition(
        task_def_id,
        name="title",
        label="Task Title",
        type="string",
        required=True,
        constraints={"max_length": 200}
    )

    manager.add_property_definition(
        task_def_id,
        name="status",
        label="Task Status",
        type="string",
        required=True,
        default_value="pending"
    )

    # 5. 创建项目实例
    project_data = {
        "name": "Website Redesign",
        "budget": 50000,
        "start_date": "2024-03-15"
    }
    project_id = manager.create_object(project_def_id, 1, project_data)

    # 6. 创建任务实例
    task_data = {
        "title": "Design Homepage",
        "status": "in_progress",
        "assigned_to": "John Doe"
    }
    task_id = manager.create_object(task_def_id, 1, task_data)

    # 7. 创建项目和任务的关系
    manager.create_relationship(project_id, task_id, "has_task")

    # 8. 查询项目
    projects = manager.query_objects(project_def_id, {"budget": "50000"})
    print("Found projects:", projects)

if __name__ == "__main__":
    main()