from abc import ABC, abstractmethod
import enum
from typing import Annotated, List, Literal, Optional, Union, Any
from pydantic import BaseModel, Field, field_validator


# DatasetConfig
class DatasetConfig(BaseModel):
    """Configuration for a dataset"""
    dataset_id: str
    dataset_name: str
    alias: str | None = None

    def to_sql(self) -> str:
        """Convert the dataset to SQL"""
        return f"{self.dataset_name} AS {self.alias}" if self.alias else self.dataset_name

    def to_alias_sql(self) -> str:
        """Convert the dataset to SQL"""
        return f"{self.alias}" if self.alias else self.dataset_name


# FieldConfig
class FieldConfig(BaseModel):
    """Configuration for a field"""
    field_id: str
    field_name: str

    def to_sql(self) -> str:
        """Convert the field to SQL"""
        return f"{self.field_name}"


# DatasetFieldConfig
class DatasetFieldConfig(BaseModel):
    """Configuration for a field in the dataset"""
    dataset: DatasetConfig
    field: FieldConfig

    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return f"{self.dataset.to_alias_sql()}.{self.field.to_sql()}"


# Column Related Schema

class ExpressionNodeType(enum.Enum):
    """Types of nodes in an expression tree"""
    FUNCTION = "function"
    field = "field"
    LITERAL = "literal"
    OPERATOR = "operator"
    CASE = "case"


class BaseExpressionNode(BaseModel, ABC):
    """Base class for all expression tree nodes"""
    type: ExpressionNodeType

    @abstractmethod
    def to_sql(self) -> str:
        """Convert the expression node to SQL"""
        pass


class LiteralExpressionNode(BaseExpressionNode):
    """A literal value in an expression"""
    type: Literal["literal"]
    value: Any
    value_type: Optional[str] = None  # e.g., "string", "number", "date", etc.

    def to_sql(self) -> str:
        """Convert the literal to SQL"""
        if isinstance(self.value, str):
            return f"'{self.value}'"
        return str(self.value)


class FieldExpressionNode(BaseExpressionNode):
    """A reference to a field in a dataset"""
    type: Literal["field"]
    dataset_field: DatasetFieldConfig
    
    def to_sql(self) -> str:
        """Convert the field reference to SQL"""
        return self.dataset_field.to_sql()


class FunctionExpressionNode(BaseExpressionNode):
    """A function call in an expression"""
    type: Literal["function"]
    function_name: str
    arguments: List["ExpressionNode"]
    
    def to_sql(self) -> str:
        """Convert the function to SQL"""
        args_sql = ", ".join(arg.to_sql() for arg in self.arguments)
        return f"{self.function_name}({args_sql})"


class OperatorExpressionNode(BaseExpressionNode):
    """An operator in an expression (e.g., +, -, *, /)"""
    type: Literal["operator"]
    operator: str
    left: "ExpressionNode"
    right: "ExpressionNode"
    
    def to_sql(self) -> str:
        """Convert the operator expression to SQL"""
        return f"({self.left.to_sql()} {self.operator} {self.right.to_sql()})"


class CaseWhenCondition(BaseModel):
    """A WHEN condition in a CASE expression"""
    condition: "ExpressionNode"
    result: "ExpressionNode"


class CaseExpressionNode(BaseExpressionNode):
    """A CASE expression"""
    type: Literal["case"]
    conditions: List[CaseWhenCondition]
    else_result: Optional["ExpressionNode"] = None
    
    def to_sql(self) -> str:
        """Convert the CASE expression to SQL"""
        when_clauses = " ".join(
            f"WHEN {cond.condition.to_sql()} THEN {cond.result.to_sql()}"
            for cond in self.conditions
        )
        else_clause = f" ELSE {self.else_result.to_sql()}" if self.else_result else ""
        return f"CASE {when_clauses}{else_clause} END"


# Define ExpressionNode after all node types are defined to handle forward references
ExpressionNode = Annotated[
    Union[
        LiteralExpressionNode, 
        FieldExpressionNode,
        FunctionExpressionNode,
        OperatorExpressionNode,
        CaseExpressionNode
    ],
    Field(discriminator='type')
]


# Column Related Schema
class ExpressionColumnConfig(BaseModel):
    """Configuration for an expression column in the dataset"""
    type: Literal["expression"]
    expression: Union[str, ExpressionNode]  # Support both string and tree structure
    alias: str | None = None
    
    def to_sql(self, include_alias: bool = True) -> str:
        """Convert the expression to SQL"""
        if isinstance(self.expression, str):
            expression_sql = self.expression
        else:
            expression_sql = self.expression.to_sql()
        if include_alias and self.alias:
            return f"{expression_sql} AS {self.alias}"
        return expression_sql


class FieldColumnConfig(BaseModel):
    """Configuration for a field in the dataset"""
    type: Literal["field"]
    dataset_field: DatasetFieldConfig
    alias: str | None = None

    def to_sql(self, include_alias: bool = True) -> str:
        """Convert the field to SQL"""
        if include_alias and self.alias:
            return f"{self.dataset_field.to_sql()} AS {self.alias}"
        return self.dataset_field.to_sql()


ColumnConfig = Annotated[
    Union[FieldColumnConfig, ExpressionColumnConfig],
    Field(discriminator='type')
]


# Join Related Schema
class JoinOperator(enum.Enum):
    EQUALS = "="

    def to_sql(self) -> str:
        """Convert the join operator to SQL"""
        return self.value


class JoinType(enum.Enum):
    LEFT = "left"
    INNER = "inner"

    def to_sql(self) -> str:
        """Convert the join type to SQL"""
        return self.value.upper()


class JoinCondition(BaseModel):
    """Defines how two tables should be joined"""
    left_dataset_field: DatasetFieldConfig
    operator: JoinOperator
    right_dataset_field: DatasetFieldConfig


class JoinConfig(BaseModel):
    """Configuration for a table join"""
    dataset: DatasetConfig
    join_type: JoinType
    condition: JoinCondition

    def __iter__(self):
        """Allow unpacking like: table, join_type, condition = join_config"""
        yield self.dataset
        yield self.join_type
        yield self.condition


# Filter Related Schema

class FilterOperator(enum.Enum):
    EQUALS = "="
    NOT_EQUALS = "!="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    GREATER_THAN_EQUALS = ">="
    LESS_THAN_EQUALS = "<="
    LIKE = "LIKE"
    IN = "IN"
    NOT_IN = "NOT IN"
    IS_NULL = "IS NULL"
    IS_NOT_NULL = "IS NOT NULL"

    def to_sql(self) -> str:
        """Convert the filter operator to SQL"""
        return self.value


class FilterConfig(BaseModel):
    """Configuration for a WHERE clause filter condition"""
    dataset_field: DatasetFieldConfig
    operator: FilterOperator
    value: Optional[Any] = None
    
    @field_validator('value')
    def validate_value(cls, v, info):
        values = info.data
        if 'operator' in values:
            operator = values['operator']
            # For IS NULL and IS NOT NULL, value should be None
            if operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL] and v is not None:
                raise ValueError(f"Value must be None for {operator.to_sql()} operator")
            # For all other operators, value should not be None
            elif operator not in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL] and v is None:
                raise ValueError(f"Value cannot be None for {operator.to_sql()} operator")
            # For IN and NOT IN, value should be a list
            if operator in [FilterOperator.IN, FilterOperator.NOT_IN] and not isinstance(v, list):
                raise ValueError(f"Value must be a list for {operator.to_sql()} operator")
        return v
    
    def to_sql(self) -> str:
        """Convert the filter to a SQL WHERE clause fragment"""
        if self.operator in [FilterOperator.IS_NULL, FilterOperator.IS_NOT_NULL]:
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()}"
        elif self.operator in [FilterOperator.IN, FilterOperator.NOT_IN]:
            values = ", ".join([f"'{v}'" if isinstance(v, str) else str(v) for v in self.value])
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} ({values})"
        elif self.operator == FilterOperator.LIKE:
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} '{self.value}'"
        else:
            # Handle string values with quotes
            value_str = f"'{self.value}'" if isinstance(self.value, str) else str(self.value)
            return f"{self.dataset_field.to_sql()} {self.operator.to_sql()} {value_str}"


class FilterLogic(enum.Enum):
    AND = "AND"
    OR = "OR"

    def to_sql(self) -> str:
        """Convert the filter logic to SQL"""
        return self.value


class FilterGroup(BaseModel):
    """Group of filter conditions with a specific logic operator"""
    filters: List[Union['FilterGroup', FilterConfig]]
    logic: FilterLogic = FilterLogic.AND

    def to_sql(self) -> str:
        """Convert the filter group to a SQL WHERE clause fragment"""
        if not self.filters:
            return ""

        filter_clauses = []
        for filter_item in self.filters:
            filter_clauses.append(filter_item.to_sql())
        
        # Join the clauses with the appropriate logic operator
        joined_clauses = f" {self.logic.to_sql()} ".join(filter_clauses)
        
        # Wrap in parentheses if there's more than one filter
        if len(self.filters) > 1:
            return f"({joined_clauses})"
        return joined_clauses


# Group By Related Schema

class TimeGranularity(enum.Enum):
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"


class FieldType(enum.Enum):
    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    DATETIME = "datetime"
    BOOLEAN = "boolean"


class GroupByConfig(BaseModel):
    """Configuration for a GROUP BY clause with time granularity support"""
    dataset_field: DatasetFieldConfig
    field_type: FieldType
    time_granularity: Optional[TimeGranularity] = None
    
    @field_validator('time_granularity')
    def validate_time_granularity(cls, v, info):
        values = info.data
        if 'field_type' in values:
            field_type = values['field_type']
            # Time granularity only applies to date/datetime fields
            if v is not None and field_type not in [FieldType.DATE, FieldType.DATETIME]:
                raise ValueError(f"Time granularity can only be applied to DATE or DATETIME fields, not {field_type.value}")
        return v
    
    def to_sql(self) -> str:
        """Convert the group by to a SQL GROUP BY clause fragment"""
        if self.field_type in [FieldType.DATE, FieldType.DATETIME] and self.time_granularity:
            # Different databases have different date truncation functions
            # This example uses PostgreSQL's date_trunc
            return f"date_trunc('{self.time_granularity.value}', {self.dataset_field.to_sql()})"
        else:
            return f"{self.dataset_field.to_sql()}"


class OrderByConfig(BaseModel):
    """Configuration for an ORDER BY clause"""
    dataset_field: DatasetFieldConfig
    direction: Literal["ASC", "DESC"] = "ASC"
    
    def __iter__(self):
        """Allow unpacking like: column, direction = order_by_config"""
        yield self.dataset_field
        yield self.direction

    def to_sql(self) -> str:
        """Convert the order by to a SQL ORDER BY clause fragment"""
        return f"{self.dataset_field.to_sql()} {self.direction}"


class QueryConfig(BaseModel):
    """Complete configuration for a query dataset"""
    columns: List[ColumnConfig] = Field(default_factory=list)
    # metrics: List[MetricConfig] = Field(default_factory=list)
    primary_dataset: DatasetConfig
    additional_datasets: List[JoinConfig] = Field(default_factory=list)
    filter_group: FilterGroup | None = None
    group_bys: List[ColumnConfig] = Field(default_factory=list)
    order_bys: List[OrderByConfig] = Field(default_factory=list)
    limit: Optional[int] = None

    @field_validator('limit')
    def validate_limit(cls, v):
        if v is not None and v <= 0:
            raise ValueError('limit must be a positive integer')
        return v

    def to_sql(self) -> str:
        # Build the SELECT clause
        query = f"SELECT {', '.join(column.to_sql() for column in self.columns)}\n"

        # Build the FROM clause
        query += f"FROM {self.primary_dataset.to_sql()}\n"


        # Build the JOIN clauses
        for dataset_config, join_type, join_condition in self.additional_datasets:
            query += f"{join_type.to_sql()} JOIN {dataset_config.to_sql()} ON "
            query += f"{join_condition.left_dataset_field.to_sql()} "
            query += f"{join_condition.operator.to_sql()} "
            query += f"{join_condition.right_dataset_field.to_sql()}\n"

        # Build the WHERE clause if filters exist
        if self.filter_group:
            query += f"WHERE {self.filter_group.to_sql()}\n"

        # Build the GROUP BY clause if group_bys exist
        if self.group_bys:
            group_by_clauses = [group_by.to_sql(include_alias=False) for group_by in self.group_bys]
            query += f"GROUP BY {', '.join(group_by_clauses)}\n"

        # Build the ORDER BY clause if order_bys exist
        if self.order_bys:
            order_clauses = [order_by.to_sql() for order_by in self.order_bys]
            query += f"ORDER BY {', '.join(order_clauses)}\n"

        # Add LIMIT clause if specified
        if self.limit is not None:
            query += f"LIMIT {self.limit}\n"

        return query.strip()


no_aggregation_data = {
    "columns": [
        {
            "type": "field",
            "dataset_field": {
                "dataset": {
                    "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                    "dataset_name": "table_1",
                    "alias": "T1"
                },
                "field": {
                    "field_id": "3f7b2a1c-9d8e-4f5g-6h7i-8j9k0l1m2n3o",
                    "field_name": "first_name",
                },
            },
            "alias": "customer_name",
        },
        {
            "type": "expression",
            "expression": {
                "type": "function",
                "function_id": "0b787e3a-c722-453f-b23e-b0e134d0c8cc",
                "function_name": "CONCAT",
                "arguments": [
                    {
                        "type": "field",
                        "dataset_field": {
                            "dataset": {
                                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                                "dataset_name": "table_1",
                                "alias": "T1"
                            },
                            "field": {
                                "field_id": "3f7b2a1c-9d8e-4f5g-6h7i-8j9k0l1m2n3o",
                                "field_name": "last_name",
                            },
                        },
                    },
                    {
                        "type": "field",
                        "dataset_field": {
                            "dataset": {
                                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                                "dataset_name": "table_1",
                                "alias": "T2"
                            },
                            "field": {
                                "field_id": "2ab3c98f-cd89-4da4-88bb-65434d0b7c24",
                                "field_name": "last_name",
                            },
                        },
                    }
                ]
            },
            "alias": "full_name"
        }
    ],
    "metrics": [],
    "primary_dataset": {
        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
        "dataset_name": "table_1",
        "alias": "T1"
    },
    "additional_datasets": [
        {
            "dataset": {
                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                "dataset_name": "table_1",
                "alias": "T2"
            },
            "join_type": "inner",
            "condition": {
                "left_dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_1",
                        "alias": "T1"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "id"
                    }
                },
                "operator": "=",
                "right_dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_2",
                        "alias": "T2"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "id"
                    }
                }
            }
        }
    ],
    "filter_group": {
        "filters": [
            {
                "dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_1",
                        "alias": "T1"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "status"
                    }
                },
                "operator": "=",
                "value": "active"
            }
        ],
        "logic": "AND"
    },
    "group_bys": [],
    "order_bys": [
        {
            "dataset_field": {
                "dataset": {
                    "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                    "dataset_name": "table_1",
                    "alias": "T1"
                },
                "field": {
                    "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                    "field_name": "created_at"
                }
            },
            "direction": "DESC"
        }
    ],
    "limit": 100
}

aggregation_data = {
    "columns": [],
    "metrics": [
        {
            "type": "expression",
            "expression": {
                "type": "function",
                "function_id": "0b787e3a-c722-453f-b23e-b0e134d0c8cc",
                "function_name": "CONCAT",
                "arguments": [
                    {
                        "type": "field",
                        "dataset_field": {
                            "dataset": {
                                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                                "dataset_name": "table_1",
                                "alias": "T1"
                            },
                            "field": {
                                "field_id": "3f7b2a1c-9d8e-4f5g-6h7i-8j9k0l1m2n3o",
                                "field_name": "last_name",
                            },
                        },
                    },
                    {
                        "type": "field",
                        "dataset_field": {
                            "dataset": {
                                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                                "dataset_name": "table_1",
                                "alias": "T2"
                            },
                            "field": {
                                "field_id": "2ab3c98f-cd89-4da4-88bb-65434d0b7c24",
                                "field_name": "last_name",
                            },
                        },
                    }
                ]
            },
            "alias": "full_name"
        }
    ],
    "primary_dataset": {
        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
        "dataset_name": "table_1",
        "alias": "T1"
    },
    "additional_datasets": [
        {
            "dataset": {
                "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                "dataset_name": "table_1",
                "alias": "T2"
            },
            "join_type": "inner",
            "condition": {
                "left_dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_1",
                        "alias": "T1"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "id"
                    }
                },
                "operator": "=",
                "right_dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_2",
                        "alias": "T2"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "id"
                    }
                }
            }
        }
    ],
    "filter_group": {
        "filters": [
            {
                "dataset_field": {
                    "dataset": {
                        "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                        "dataset_name": "table_1",
                        "alias": "T1"
                    },
                    "field": {
                        "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                        "field_name": "status"
                    }
                },
                "operator": "=",
                "value": "active"
            }
        ],
        "logic": "AND"
    },
    "group_bys": [
        {
            "type": "field",
            "dataset_field": {
                "dataset": {
                    "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                    "dataset_name": "table_1",
                    "alias": "T1"
                },
                "field": {
                    "field_id": "3f7b2a1c-9d8e-4f5g-6h7i-8j9k0l1m2n3o",
                    "field_name": "first_name",
                },
            },
            "alias": "customer_name"
        }
    ],
    "order_bys": [
        {
            "dataset_field": {
                "dataset": {
                    "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
                    "dataset_name": "table_1",
                    "alias": "T1"
                },
                "field": {
                    "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
                    "field_name": "created_at"
                }
            },
            "direction": "DESC"
        }
    ],
    "limit": 100
}


query_config = QueryConfig(**aggregation_data)
print(query_config.to_sql())
# print(query_config)