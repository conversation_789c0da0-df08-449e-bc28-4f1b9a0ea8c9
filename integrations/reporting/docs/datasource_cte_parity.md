Storyboard: DataSource-CTE Parity Animation

Scene 1: Introduction
Frame 1: Title screen "Understanding DataSource-CTE Parity"
Frame 2: Split screen showing a database icon on the left and SQL code on the right
Frame 3: Text appears: "Each DataSource in our system can be represented as a SQL Common Table Expression (CTE)"

Scene 2: Table DataSource
Frame 1: Show a database table icon labeled "product_dim"
Frame 2: The table icon transforms into a code block:
```sql
WITH product_dimension AS (
    SELECT * FROM product_dim
)
```
- Frame 3: Text appears: "A Table DataSource directly references an existing table"

Scene 3: Custom DataSource
Frame 1: Show a gear icon labeled "Sales Summary"
Frame 2: Show arrows connecting from a table icon to the gear icon
Frame 3: The gear icon transforms into a code block:
```sql
WITH sales_summary AS (
    SELECT 
        product_id,
        customer_id,
        SUM(amount) as total_sales
    FROM sales_fact
    GROUP BY product_id, customer_id
)
```
- Frame 4: Text appears: "A Custom DataSource is built from other DataSources using SQL operations"

Scene 4: Template DataSource
Frame 1: Show a template icon with placeholders labeled "Monthly Sales Template"
Frame 2: Show parameters being filled in (e.g., start_date = '2023-01-01', end_date = '2023-12-31')
Frame 3: The template transforms into a code block:
```sql
WITH monthly_sales AS (
    SELECT 
        date_trunc('month', order_date) as month,
        product_category,
        SUM(sales_amount) as monthly_sales
    FROM orders
    WHERE order_date BETWEEN '2023-01-01' AND '2023-12-31'
    GROUP BY date_trunc('month', order_date), product_category
)
```
Frame 4: Text appears: "A Template DataSource uses a predefined SQL query with parameters"

Scene 5: Chaining DataSources
Frame 1: Show three separate DataSource icons
Frame 2: Show arrows connecting the DataSources in a dependency graph
Frame 3: The DataSources transform into a series of CTEs:
```sql
WITH 
datasource_1 AS (
    SELECT * FROM table_1
),

datasource_2 AS (
    SELECT id, name FROM datasource_1 WHERE active = true
),

datasource_3 AS (
    SELECT id, COUNT(*) as count FROM datasource_2 GROUP BY id
)
```
- Frame 4: Text appears: "DataSources can be chained together, with each one building on the previous"

Scene 6: Building a Report
Frame 1: Show a report icon with multiple DataSource icons feeding into it
Frame 2: The report and DataSources transform into a complete SQL query with CTEs:
```sql
WITH 
datasource_1 AS (
    SELECT * FROM table_1
),

datasource_2 AS (
    SELECT * FROM table_2
),

datasource_3 AS (
    SELECT 
        d1.id, 
        d1.name,
        d2.value
    FROM datasource_1 d1
    JOIN datasource_2 d2 ON d1.id = d2.id
)

SELECT * FROM datasource_3
```
- Frame 3: Text appears: "A Report combines multiple DataSources into a final query"

Scene 7: Database Representation
Frame 1: Show the database tables for storing DataSources
Frame 2: Highlight the fields: id, name, type, table_name, query_config, query_sql
Frame 3: Show how a DataSource definition is stored in JSON format
Frame 4: Show the transformation from the stored JSON to a CTE
Frame 5: Text appears: "The system stores DataSource definitions and converts them to CTEs at runtime"

Scene 8: Execution Flow
Frame 1: Show a user clicking "Run Report"
Frame 2: Show the system loading DataSource definitions
Frame 3: Show the system building a dependency graph
Frame 4: Show the system generating the CTE-based SQL
Frame 5: Show the SQL being executed against the database
Frame 6: Show results returning to the user
Frame 7: Text appears: "When a report is executed, the system generates and runs a single SQL query with CTEs"

Scene 9: Benefits
Frame 1: Text appears: "Benefits of DataSource-CTE Parity"
Frame 2: Show icons for:
Modularity (puzzle pieces)
Readability (magnifying glass)
Optimization (speedometer)
Dependency Management (connected nodes)
Flexibility (bendable ruler)
Debugging (bug with magnifying glass)

Scene 10: Conclusion
Frame 1: Show a complete system with DataSources, Reports, and the database
Frame 2: Text appears: "DataSource-CTE Parity provides a powerful foundation for building flexible reporting systems"
Frame 3: Final screen with "Questions?" and contact information




我们可以将每个Datasource视为一个数据表（如下图①所示）；对于不同类型的Datasource的数据表定义可以用不同的字段进行存储（如下图②所示）；对于每一种类型的Datasource我们可以在查询引擎翻译成一个SQL查询语句（如下图③所示）。

特殊的，用户可以递归地从一个或多个已存在Datasource中定制自己的Datasource和衍生字段，我们希望给用户不仅仅只包括join多个来源Datasource的能力，也允许其能使用query_config来构造数据聚合等完整的SQL能力。

通过对外暴露出Dataset Fields的定义并将Dataset Fields的实现保留在内部，只要上游的Dataset Fields保持一致，那么下游的Dataset链就不会出错

通过跟踪Dataset的每个Field的来源，我们可以得到所有Field的整体的血统图，对于下图中pipeline_duration的duration字段，它是一个衍生字段并来源于一个或多个上游Dataset的一个或多个字段，其值通过某个函数表达式计算得到。

通过字段的血统图，我们也能到Dataset的血统图。

通过Field的血统，报表系统可以提供一个接口以检查某个上游字段是否被下游报表使用，以供下列两种场景使用：
1. Postgres业务数据库进行字段更改和删除的时候，可以在Pytest测试用例中检查线上系统和线下系统是否有下游Dataset依赖该字段，防止破坏下游报表。
2. 上游Dataset进行字段更改和删除的时候，可以检查线上系统和线下系统是否有报表依赖该字段，防止破坏下游报表。

本文档是另篇文档的补充和优化。
主要的改进如下：
1. 讲解了报表系统的核心概念
1. 合并datasource和dataset两个表到一个表dataset
2. 去除Report表的query_config字段并下推至dataset表中
3. 提出了另一个Custom Object的解决方案
4. 提出了Dataset及其Field的血统，以此作为Dataset Compatibility Check的解决方案

既然Dataset可以视为一个SQL查询，而其所依赖的每个上游Dataset都是一个CTE，那么我们可以给Dataset增加一个开关字段表示是否对该Dataset同步进数据仓库以实现预计算。一旦该Dataset的同步开关置真，我们可以安全地将该Dataset的CTE去除并直接引用数仓表。

用户可以在Standard Object之外定义的Custom Object，对于Custom Object/Field/Record/Relation的变动，报表系统会做如下应对：
1. 对于Custom Object的插入，会异步地（在CDC队列后，在数仓中）创建Dataset。
2. 对于Custom Object的修改和删除，会同步检查Dataset依赖性，通过后会异步地在数仓中更新和删除Dataset。
3. 对于Custom Field的插入，会异步地创建DatasetField。
4. 对于Custom Field的修改和删除，会同步检查Dataset依赖性，通过后会异步地在数仓中更新和删除DatasetField。
5. 对于Custom Record的插入、修改和删除，会异步地在Dataset中插入、修改和删除一条记录。



```json
{
  "select": [
    {
      "field_id": "3f7b2a1c-9d8e-4f5g-6h7i-8j9k0l1m2n3o",
      "alias": "customer_name",
      "expression": "CONCAT(first_name, ' ', last_name)"
    }
  ],
  "from": {
    "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
    "dataset_name": "table_1"
  },
  "joins": [
    {
      "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
      "dataset_name": "table_2",
      "join_type": "inner",
      "condition": {
        "left_dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
        "left_dataset_name": "table_1",
        "left_field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
        "operator": "=",
        "right_dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
        "right_dataset_name": "table_2",
        "right_field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1"
      }
    }
  ],
  "where": [
    {
      "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
      "dataset_name": "table_1",
      "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
      "operator": "=",
      "value": "active"
    }
  ],
  "group_by": [
    {
      "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
      "dataset_name": "table_1",
      "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1"
    }
  ],
  "order_by": [
    {
      "dataset_id": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
      "dataset_name": "table_1",
      "field_id": "p6o5n4m3-l2k1-j0i9-h8g7-f6e5d4c3b2a1",
      "direction": "DESC"
    }
  ],
  "limit": 100
}
```

大体有两种使用QueryEngine的场景，一种是对于创建新报表时预览，另外一种是对于已存在的报表进行筛选器的调整并展示报表。

模板Dataset是用在由Reevo预定义的Template报表中的，它使用包含了Jinja模板引擎的sql语句实现SQL逻辑的复杂性和动态性。结合用户在报表页面上的动态Filters，查询引擎可以动态地创建sql以支持复杂的业务查询。






In the Reevo engineers alter table scenario, we can make Dataset Compatibility Check not enforced as to not slow down team development, but still we still a mechanism to inform Reevo engineers when he accidentally alter a table in OLTP database which will fail the whole ETL workflow and . 

