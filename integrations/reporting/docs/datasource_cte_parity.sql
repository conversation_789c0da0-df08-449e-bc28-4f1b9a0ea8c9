SELECT * FROM table_1;

WITH
datasource_1 AS (
    SELECT * FROM table_1
),
datasource_2 AS (
    SELECT * FROM table_2
)
SELECT * FROM datasource_1
JOIN datasource_2 ON datasource_1.id = datasource_2.id
WHERE datasource_1.id = 1
GROUP BY datasource_1.id
ORDER BY datasource_1.id DESC
LIMIT 10;


SELECT * FROM table_1
JOIN table_2 ON table_1.id = table_2.id
WHERE table_1.id = 1
GROUP BY table_1.id
ORDER BY table_1.id DESC
LIMIT 10;

WITH
datasource_1 AS (
    SELECT * FROM table_1
),
datasource_2 AS (
    SELECT * FROM table_2
),
datasource_3 AS (
    SELECT * FROM datasource_1
    JOIN datasource_2 ON datasource_1.id = datasource_2.id
    WHERE datasource_1.id = 1
    GROUP BY datasource_1.id
    ORDER BY datasource_1.id DESC
    LIMIT 10
)
SELECT * FROM datasource_1
JOIN datasource_3 ON datasource_1.id = datasource_3.id
WHERE datasource_1.id = 1
