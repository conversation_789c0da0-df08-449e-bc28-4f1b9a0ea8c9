from uuid import UUID

from integrations.reporting.query_type import QueryConfig

VALID_TABLES = ["contact", "account", "pipeline", "activity"]

class QueryBuilderService:
    def __init__(self):
        self.valid_tables = VALID_TABLES

    def validate_config(self, config: QueryConfig):
        """
        Validates a QueryConfig object to ensure it contains valid tables and columns.
        
        Args:
            config (QueryConfig): The configuration to validate
            
        Returns:
            bool: True if valid, raises ValueError otherwise
        """
        # Check if base table is valid
        if config.base_table.table_name not in self.valid_tables:
            raise ValueError(f"Base table '{config.base_table.table_name}' is not valid. Valid tables: {self.valid_tables}")
        
        # Check additional tables
        for table_config, join_type, join_condition in config.additional_tables:
            if table_config.table_name not in self.valid_tables:
                raise ValueError(f"Additional table '{table_config.table_name}' is not valid. Valid tables: {self.valid_tables}")
            
            # Validate join condition references valid tables
            if join_condition.left_table not in [config.base_table.alias] + [t.alias for t, _, _ in config.additional_tables]:
                raise ValueError(f"Join condition references unknown left table: {join_condition.left_table}")
            if join_condition.right_table != table_config.alias:
                raise ValueError(f"Join condition right table ({join_condition.right_table}) must match the table being joined ({table_config.alias})")
        
        # Additional validations could be added here:
        # - Check if columns exist in the tables
        # - Validate filter conditions
        # - Validate group_by and order_by columns

        return True
    
    def build_query(self, config: QueryConfig):
        """
        Builds a SQL query from a QueryConfig object.
        
        Args:
            config (QueryConfig): The configuration to build a query from
            
        Returns:
            str: The SQL query
        """
        # Validate the config first
        # self.validate_config(config)
        
        # Build the SELECT clause
        query = f"SELECT {', '.join(column.to_sql() for column in config.columns)}\n"

        # Build the FROM clause
        query += f"FROM {config.primary_dataset.to_sql()}\n"


        # Build the JOIN clauses
        for dataset_config, join_type, join_condition in config.additional_datasets:
            query += f"{join_type.to_sql()} JOIN {dataset_config.to_sql()} ON "
            query += f"{join_condition.left_dataset_field.to_sql()} "
            query += f"{join_condition.operator.to_sql()} "
            query += f"{join_condition.right_dataset_field.to_sql()}\n"

        # Build the WHERE clause if filters exist
        if config.filter_group:
            query += f"WHERE {config.filter_group.to_sql()}\n"

        # Build the GROUP BY clause if group_bys exist
        if config.group_bys:
            group_by_clauses = [group_by.to_sql() for group_by in config.group_bys]
            query += f"GROUP BY {', '.join(group_by_clauses)}\n"

        # Build the ORDER BY clause if order_bys exist
        if config.order_bys:
            order_clauses = [order_by.to_sql() for order_by in config.order_bys]
            query += f"ORDER BY {', '.join(order_clauses)}\n"

        # Add LIMIT clause if specified
        if config.limit is not None:
            query += f"LIMIT {config.limit}\n"
        
        return query.strip()

    def merge_configs(self, primary_config: QueryConfig, secondary_config: QueryConfig) -> QueryConfig:
        """
        Merges two QueryConfig objects, with the primary config taking precedence.
        
        Args:
            primary_config (QueryConfig): The primary configuration (takes precedence)
            secondary_config (QueryConfig): The secondary/base configuration
            
        Returns:
            QueryConfig: A new merged QueryConfig object
        """
        # Create a new config object
        merged_config = QueryConfig()
        
        # Base table - prefer primary if set, otherwise use secondary
        merged_config.base_table = primary_config.base_table or secondary_config.base_table
        
        # Merge additional tables (combine both, with primary tables first)
        merged_config.additional_tables = list(primary_config.additional_tables)
        # Add secondary tables that aren't already in primary (based on table alias)
        primary_aliases = {table.alias for table, _, _ in primary_config.additional_tables}
        for table_config, join_type, join_condition in secondary_config.additional_tables:
            if table_config.alias not in primary_aliases:
                merged_config.additional_tables.append((table_config, join_type, join_condition))
        
        # Columns - prefer primary if set, otherwise use secondary
        merged_config.columns = primary_config.columns or secondary_config.columns
        
        # Filters - combine both sets of filters if using individual filters
        if primary_config.filters or secondary_config.filters:
            merged_config.filters = list(primary_config.filters)
            if secondary_config.filters:
                merged_config.filters.extend(secondary_config.filters)
        
        # Filter group - prefer primary if set, otherwise use secondary
        merged_config.filter_group = primary_config.filter_group or secondary_config.filter_group
        
        # Group by - combine both sets
        merged_config.group_bys = list(primary_config.group_bys)
        if secondary_config.group_bys:
            merged_config.group_bys.extend(secondary_config.group_bys)
        
        # Order by - prefer primary if set, otherwise use secondary
        merged_config.order_bys = primary_config.order_bys or secondary_config.order_bys
        
        # Limit - prefer primary if set, otherwise use secondary
        merged_config.limit = primary_config.limit if primary_config.limit is not None else secondary_config.limit
        
        return merged_config

    def build_datasource_query(self, datasource_id: UUID, config: QueryConfig):
        """
        Builds a SQL query from a QueryConfig object for a datasource.
        
        Args:
            datasource_id (UUID): The ID of the datasource to build a query for
            config (QueryConfig): The additional parameters to apply to the query
            
        Returns:
            str: The SQL query
        """
        pass

    def build_dataset_query(self, dataset_id: UUID, config: QueryConfig):
        """
        Builds a SQL query from a QueryConfig object for a dataset.

        Args:
            dataset_id (UUID): The ID of the dataset to build a query for
            config (QueryConfig): The additional parameters to apply to the query
            
        Returns:
            str: The SQL query
        """
        pass

    def build_report_query(self, report_id: UUID, config: QueryConfig):
        """
        Builds a SQL query from a QueryConfig object for a report.

        Args:
            report_id (UUID): The ID of the report to build a query for
            config (QueryConfig): The additional parameters to apply to the query
            
        Returns:
            str: The SQL query
        """
        pass
