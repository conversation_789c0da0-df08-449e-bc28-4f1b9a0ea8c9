from integrations.reporting.query_type import (
    JoinType, JoinCondition, JoinConfig, FilterOperator, 
    FilterConfig, FilterLogic, FilterGroup, TimeGranularity, FieldType, 
    GroupByConfig, OrderByConfig, QueryConfig
)
from integrations.reporting.query_builder_service import QueryBuilderService

def test_simple_query_config():
    # Create a simple QueryConfig with just a base table
    config = QueryConfig(
        name="Simple Contacts Query",
        base_table=TableConfig(
            table_name="contact",
            alias="c",
            columns=["id", "name", "email"]
        ),
        limit=100
    )
    
    # Initialize the builder
    builder = QueryBuilderService()
    
    # Build the query
    query = builder.build_query(config)
    
    # Print the result
    print("\nSimple Query:")
    print(query)

    # Assertions
    assert "SELECT c.id, c.name, c.email" in query
    assert "FROM contact AS c" in query
    assert "LIMIT 100" in query

def test_complex_query_config():
    # Create a complex QueryConfig with joins, filters, group by, and order by
    config = QueryConfig(
        name="Complex Sales Analysis",
        base_table=TableConfig(
            table_name="contact",
            alias="c",
            columns=["id", "name"]
        ),
        additional_tables=[
            JoinConfig(
                table=TableConfig(
                    table_name="account",
                    alias="a",
                    columns=["id", "name", "industry"]
                ),
                join_type=JoinType.INNER,
                condition=JoinCondition(
                    left_table="c",
                    left_column="account_id",
                    right_table="a",
                    right_column="id"
                )
            ),
            JoinConfig(
                table=TableConfig(
                    table_name="activity",
                    alias="act",
                    columns=["id", "type", "date"]
                ),
                join_type=JoinType.LEFT,
                condition=JoinCondition(
                    left_table="c",
                    left_column="id",
                    right_table="act",
                    right_column="contact_id"
                )
            )
        ],
        filter_group=FilterGroup(
            logic=FilterLogic.AND,
            filters=[
                FilterConfig(
                    table="a",
                    column="industry",
                    operator=FilterOperator.IN,
                    value=["Technology", "Healthcare"]
                ),
                FilterGroup(
                    logic=FilterLogic.OR,
                    filters=[
                        FilterConfig(
                            table="act",
                            column="type",
                            operator=FilterOperator.EQUALS,
                            value="email"
                        ),
                        FilterConfig(
                            table="act",
                            column="type",
                            operator=FilterOperator.EQUALS,
                            value="call"
                        )
                    ]
                ),
                FilterConfig(
                    table="act",
                    column="date",
                    operator=FilterOperator.GREATER_THAN,
                    value="2023-01-01"
                )
            ]
        ),
        group_bys=[
            GroupByConfig(
                table="a",
                column="industry",
                field_type=FieldType.TEXT
            ),
            GroupByConfig(
                table="act",
                column="date",
                field_type=FieldType.DATE,
                time_granularity=TimeGranularity.MONTH
            )
        ],
        order_bys=[
            OrderByConfig(
                column="a.industry",
                direction="ASC"
            ),
            OrderByConfig(
                column="date_trunc('month', act.date)",
                direction="DESC"
            )
        ],
        limit=1000
    )

    # Initialize the builder
    builder = QueryBuilderService()
    
    # Build the query
    query = builder.build_query(config)
    
    # Print the result
    print("\nComplex Query:")
    print(query)
    
    # Assertions
    assert "SELECT" in query
    assert "FROM contact AS c" in query
    assert "INNER JOIN account AS a" in query
    assert "LEFT JOIN activity AS act" in query
    assert "WHERE" in query
    assert "a.industry IN ('Technology', 'Healthcare')" in query
    assert "GROUP BY" in query
    assert "ORDER BY" in query
    assert "LIMIT 1000" in query


def test_query_config_with_dataset():
    # Create a QueryConfig with a dataset
    from integrations.reporting.query_type import query_config as config

    # Initialize the builder
    builder = QueryBuilderService()
    
    # Build the query
    query = builder.build_query(config)
    
    # Print the result
    print("\nCustom Query:")
    print(query)
    

if __name__ == "__main__":
    # test_simple_query_config()
    # test_complex_query_config()
    test_query_config_with_dataset()


