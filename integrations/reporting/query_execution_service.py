from uuid import UUID
from pydantic import BaseModel

from integrations.reporting.materialize_connection import AsyncMaterializeConnection
from integrations.reporting.query_builder_service import QueryBuilderService
from integrations.reporting.query_type import QueryConfig


class QueryExecutionService:
    def __init__(self, db_connection: AsyncMaterializeConnection):
        self.db_connection = db_connection
        self.query_builder_service = QueryBuilderService()

    async def execute_query(self, config: QueryConfig):
        """Execute the dataset query and return results"""
        query = self.query_builder_service.build_query(config)
        return await self.db_connection.execute_query(query)

    async def execute_datasource(self, datasource_id: UUID, config: QueryConfig):
        """Execute the datasource query and return results"""
        query = self.query_builder_service.build_datasource_query(datasource_id, config)
        return await self.db_connection.execute_query(query)

    async def execute_dataset(self, dataset_id: UUID, config: QueryConfig):
        """Execute the dataset query and return results"""
        query = self.query_builder_service.build_dataset_query(dataset_id, config)
        return await self.db_connection.execute_query(query)

    async def execute_report(self, report_id: UUID, config: QueryConfig):
        """Execute the report query and return results"""
        query = self.query_builder_service.build_report_query(report_id, config)
        return await self.db_connection.execute_query(query)
