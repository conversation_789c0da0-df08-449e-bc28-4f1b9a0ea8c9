"""
Test the query execution service.

Usage:
    python test_query_execution_service.py

Requirements:
    pip install asyncpg
"""

import asyncio
import logging
from integrations.reporting.materialize_connection import AsyncMaterializeConnection
from integrations.reporting.query_execution_service import QueryExecutionService
from integrations.reporting.query_type import (
    TableConfig, Join<PERSON>ype, JoinCondition, JoinConfig, FilterOperator, 
    FilterConfig, FilterLogic, FilterGroup, TimeGranularity, FieldType, 
    GroupByConfig, OrderByConfig, QueryConfig
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_query_execution_service")

connection_params = {
    "user": '<EMAIL>',
    "password": 'mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9',
    "database": 'mz_db_reevo_main_dev',
    "host": 'c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud',
    "port": 6875,
    "ssl": True
}

async def test_simple_query_config():
    """Run a test of the query execution service"""
    query_execution_service = QueryExecutionService(AsyncMaterializeConnection(**connection_params))
    config = QueryConfig(
        name="Simple Contacts Query",
        base_table=TableConfig(
            table_name="contact",
            alias="c",
            columns=["id", "display_name"]
        ),
        limit=10
    )
    results = await query_execution_service.execute_query(config)
    logger.info(f"Query results size: {len(results)}")
    for row in results:
        logger.info(f"{row}")


if __name__ == "__main__":
    asyncio.run(test_simple_query_config())
