import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from integrations.reporting.materialize_connection import AsyncMaterializeConnection

# Test configuration
TEST_CONFIG = {
    "host": "localhost",
    "port": 6875,
    "user": "materialize",
    "password": "password",
    "database": "materialize"
}

@pytest.fixture
def mock_asyncpg_connection():
    """Create a mock asyncpg connection"""
    mock_conn = AsyncMock()
    
    # Mock fetch method to return sample data
    async def mock_fetch(query, *args):
        if "information_schema.tables" in query:
            return [{"table_name": "test_table1"}, {"table_name": "test_table2"}]
        elif "information_schema.columns" in query:
            return [
                {"column_name": "id", "data_type": "integer"},
                {"column_name": "name", "data_type": "text"}
            ]
        else:
            return [{"id": 1, "name": "Test"}, {"id": 2, "name": "Example"}]
    
    mock_conn.fetch = mock_fetch
    
    # Mock execute method
    mock_conn.execute = AsyncMock(return_value=None)
    
    return mock_conn

@pytest.fixture
def mock_connection(mock_asyncpg_connection):
    """Create a mock AsyncMaterializeConnection with patched asyncpg.connect"""
    with patch('asyncpg.connect', AsyncMock(return_value=mock_asyncpg_connection)):
        connection = AsyncMaterializeConnection(**TEST_CONFIG)
        yield connection

@pytest.mark.asyncio
async def test_connect(mock_connection):
    """Test connecting to Materialize"""
    success = await mock_connection.connect()
    assert success is True
    assert mock_connection.connection is not None

@pytest.mark.asyncio
async def test_disconnect(mock_connection):
    """Test disconnecting from Materialize"""
    await mock_connection.connect()
    await mock_connection.disconnect()
    assert mock_connection.connection is None

@pytest.mark.asyncio
async def test_execute_select_query(mock_connection):
    """Test executing a SELECT query"""
    await mock_connection.connect()
    
    success, results, error = await mock_connection.execute_query("SELECT * FROM test_table")
    
    assert success is True
    assert len(results) == 2
    assert results[0]["id"] == 1
    assert results[0]["name"] == "Test"
    assert error is None

@pytest.mark.asyncio
async def test_execute_non_select_query(mock_connection):
    """Test executing a non-SELECT query"""
    await mock_connection.connect()
    
    success, results, error = await mock_connection.execute_query("INSERT INTO test_table VALUES (1, 'Test')")
    
    assert success is True
    assert len(results) == 0
    assert error is None

@pytest.mark.asyncio
async def test_execute_query_with_params(mock_connection):
    """Test executing a query with parameters"""
    await mock_connection.connect()
    
    success, results, error = await mock_connection.execute_query(
        "SELECT * FROM test_table WHERE id = $1", 
        (1,)
    )
    
    assert success is True
    assert error is None

@pytest.mark.asyncio
async def test_execute_query_connection_error():
    """Test handling connection errors during query execution"""
    # Create a connection that will fail
    with patch('asyncpg.connect', side_effect=Exception("Connection error")):
        connection = AsyncMaterializeConnection(**TEST_CONFIG)
        success, results, error = await connection.execute_query("SELECT * FROM test_table")
        
        assert success is False
        assert "Not connected to Materialize" in error

@pytest.mark.asyncio
async def test_get_table_schema(mock_connection):
    """Test getting a table schema"""
    await mock_connection.connect()
    
    schema = await mock_connection.get_table_schema("test_table")
    
    assert len(schema) == 2
    assert schema["id"] == "integer"
    assert schema["name"] == "text"

@pytest.mark.asyncio
async def test_list_tables(mock_connection):
    """Test listing tables"""
    await mock_connection.connect()
    
    tables = await mock_connection.list_tables()
    
    assert len(tables) == 2
    assert "test_table1" in tables
    assert "test_table2" in tables

@pytest.mark.asyncio
async def test_async_context_manager():
    """Test using the connection as an async context manager"""
    with patch('asyncpg.connect', AsyncMock()) as mock_connect:
        mock_connect.return_value = AsyncMock()
        mock_connect.return_value.close = AsyncMock()
        
        async with AsyncMaterializeConnection(**TEST_CONFIG) as connection:
            assert connection.connection is not None
        
        # Check that connect and close were called
        mock_connect.assert_called_once()
        mock_connect.return_value.close.assert_called_once()

@pytest.mark.asyncio
async def test_reconnect_on_query_error(mock_connection):
    """Test automatic reconnection on connection errors during query"""
    await mock_connection.connect()
    
    # Make the first query fail with a connection error
    original_fetch = mock_connection.connection.fetch
    
    call_count = 0
    async def mock_fetch_with_error(query, *args):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            raise Exception("connection lost")
        return await original_fetch(query, *args)
    
    mock_connection.connection.fetch = mock_fetch_with_error
    
    # Execute query which should fail and then reconnect
    success, results, error = await mock_connection.execute_query("SELECT * FROM test_table")
    
    assert success is True
    assert len(results) == 2
    assert error is None