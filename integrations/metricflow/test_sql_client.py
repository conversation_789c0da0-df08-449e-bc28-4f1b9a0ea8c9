from dbt.config import RuntimeConfig
from dbt.adapters.postgres import PostgresAdapter
from dbt_metricflow.cli.dbt_connectors.adapter_backed_client import AdapterBackedSqlClient
from dbt.adapters.factory import get_adapter, register_adapter, reset_adapters

# Create a minimal dbt config
config = RuntimeConfig.from_args({
    'project_name': 'test_project',
    'profile_name': 'test_profile',
    'target_path': 'target',
    'config-version': 2,
    'quoting': {},
    'models': {},
    'credentials': {
        'type': 'postgres',
        'host': 'localhost',  # replace with your host
        'port': 5432,        # replace with your port
        'user': 'your_user', # replace with your username
        'password': 'your_password', # replace with your password
        'database': 'your_database', # replace with your database
        'schema': 'public'   # replace with your schema
    }
})

# # Reset any existing adapters
# reset_adapters()

# # Register and get the Postgres adapter
# register_adapter(config)
adapter = PostgresAdapter(config=config)

# Create the SQL client
sql_client = AdapterBackedSqlClient(adapter=adapter)

print(sql_client)
quit()

from dbt_metricflow.cli.dbt_connectors.adapter_backed_client import AdapterBackedSqlClient
from dbt.adapters.factory import get_adapter_by_type
from dbt.adapters.factory import get_adapter, register_adapter, reset_adapters

sql_client = AdapterBackedSqlClient(adapter=get_adapter_by_type("postgres"))

print(sql_client)
quit()