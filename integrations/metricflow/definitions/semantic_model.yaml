semantic_models:
  - name: orders
    description: Order data
    defaults:
      agg_time_dimension: ds
    metadata:
      owner: data_team
    
    dimensions:
      - name: ds
        type: time
        description: The date of the order
        expr: time_column
      - name: customer_country
        type: categorical
        description: Customer country
        expr: country_column
    
    measures:
      - name: revenue
        description: Total revenue
        expr: sum(amount)
      - name: orders
        description: Count of orders
        expr: count(distinct order_id)
