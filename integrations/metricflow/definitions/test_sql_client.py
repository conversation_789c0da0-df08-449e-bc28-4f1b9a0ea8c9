from metricflow.sql.sql_client import SqlClient
from metricflow.sql.sql_engine import SqlEngine
from sqlalchemy import create_engine

# Create a SQLAlchemy engine for PostgreSQL
sqlalchemy_engine = create_engine(
    "postgresql://username:password@hostname:port/database_name"
)

# Create a MetricFlow SqlEngine using the SQLAlchemy engine
sql_engine = SqlEngine(sqlalchemy_engine)

# Create the SqlClient using the SqlEngine
sql_client = SqlClient(sql_engine)
