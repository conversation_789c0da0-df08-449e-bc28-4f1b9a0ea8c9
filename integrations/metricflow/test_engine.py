import datetime
from metricflow.engine.metricflow_engine import MetricFlowEngine
from metricflow.engine.metricflow_engine import MetricFlowQueryRequest
from metricflow_semantics.model.semantic_manifest_lookup import SemanticManifestLookup
from dbt_metricflow.cli.dbt_connectors.adapter_backed_client import AdapterBackedSqlClient
from dbt_metricflow.cli.dbt_connectors.dbt_config_accessor import dbtArtifacts, dbtProjectMetadata
import app.dbt_connector_patch


# dbt_project_path = "/Users/<USER>/git/reevo-ng/mf_tutorial_project"
# dbt_project_path = "/Users/<USER>/git/dbt_data"
dbt_project_path = "/Users/<USER>/git/reevo-ng/analytic"
dbt_project_metadata = dbtProjectMetadata.load_from_project_path(dbt_project_path)
dbt_artifacts = dbtArtifacts.load_from_project_metadata(dbt_project_metadata)
sql_client = AdapterBackedSqlClient(dbt_artifacts.adapter)
semantic_manifest_lookup = SemanticManifestLookup(dbt_artifacts.semantic_manifest)
engine = MetricFlowEngine(
    semantic_manifest_lookup=semantic_manifest_lookup,
    sql_client=sql_client,
)

params = [
    # {
    #     "metric_names": ["transactions"],
    #     "group_by_names": ["metric_time", "customer__customer_country"],
    #     "limit": None,
    #     "time_constraint_start": None,
    #     "time_constraint_end": None,
    #     "where_constraints": None,
    #     "order_by_names": ["metric_time"]
    # },
    # {
    #     "metric_names": ["transactions"],
    #     "group_by_names": ["metric_time__week"],
    #     "limit": None,
    #     "time_constraint_start": None,
    #     "time_constraint_end": None,
    #     "where_constraints": None,
    #     "order_by_names": ["metric_time__week"]
    # },
    # {
    #     "metric_names": ["transactions", "transaction_usd_na"],
    #     "group_by_names": ["metric_time", "transaction__is_large"],
    #     "limit": None,
    #     "time_constraint_start": datetime.datetime(2022, 3, 20),
    #     "time_constraint_end": datetime.datetime(2022, 4, 1),
    #     "where_constraints": None,
    #     "order_by_names": ["metric_time"]
    # },
    {
        "metric_names": ["pipelines"],
        "group_by_names": ["metric_time__month"],
        "limit": None,
        "time_constraint_start": None,
        "time_constraint_end": None,
        "where_constraints": ["{{ Dimension('contact__stage_id') }} = '11a38a43-e76b-40b7-8121-28a782be6b05'"],
        "order_by_names": ["metric_time__month"]
    },
]
for param in params:
    print(param)
    mf_request = MetricFlowQueryRequest.create_with_random_request_id(
        saved_query_name=None,
        metric_names=param["metric_names"],
        group_by_names=param["group_by_names"],
        limit=param["limit"],
        time_constraint_start=param["time_constraint_start"],
        time_constraint_end=param["time_constraint_end"],
        where_constraints=param["where_constraints"],
        order_by_names=param["order_by_names"],
    )
    explain_result = engine.explain(mf_request=mf_request)
    print(explain_result.sql_statement.sql)
    # query_result = engine.query(mf_request=mf_request)
    # print(query_result)
    print("=" * 30)
