import os
from pathlib import Path
from typing import Dict, Any

from dbt.config import RuntimeConfig
from dbt.adapters.factory import get_adapter, register_adapter, reset_adapters
from dbt.mp_context import get_mp_context
from dbt.context.providers import generate_runtime_macro_context
from dbt.parser.manifest import ManifestLoader
from dbt.adapters.postgres import PostgresAdapter
from typing import Generator
from dbt.tests.unit.utils import 


def runtime_config(project: Project, profile: Profile, set_test_flags) -> RuntimeConfig:
    args = get_flags()
    return RuntimeConfig.from_parts(
        project=project,
        profile=profile,
        args=args,
    )

def postgres_adapter(
    runtime_config: RuntimeConfig | None = None
) -> Generator[PostgresAdapter, None, None]:
    register_adapter(runtime_config, get_mp_context())
    adapter = get_adapter(runtime_config)
    assert isinstance(adapter, PostgresAdapter)

    manifest = ManifestLoader.load_macros(
        runtime_config,
        adapter.connections.set_query_header,
        base_macros_only=True,
    )

    adapter.set_macro_resolver(manifest)
    adapter.set_macro_context_generator(generate_runtime_macro_context)

    yield adapter
    adapter.cleanup_connections()
    reset_adapters()

gen_postgres_adapter = postgres_adapter()
postgres_adapter = next(gen_postgres_adapter)
print(postgres_adapter)
quit()



def create_dbt_config(
    credentials: Dict[str, Any],
    project_dir: str = None,
    profiles_dir: str = None,
) -> RuntimeConfig:
    """Create a minimal dbt config for standalone usage.
    
    Args:
        credentials: Dictionary containing database credentials
        project_dir: Optional path to dbt project directory
        profiles_dir: Optional path to profiles.yml directory
    """
    if not project_dir:
        project_dir = os.getcwd()
    
    if not profiles_dir:
        profiles_dir = str(Path.home() / ".dbt")  # Default dbt profiles location

    # Ensure the profiles directory exists
    os.makedirs(profiles_dir, exist_ok=True)

    # Basic dbt config structure
    config_dict = {
        'config-version': 2,
        'profile_name': 'standalone_profile',
        'target_path': 'target',
        'project_name': 'standalone_project',
        'project_dir': '/Users/<USER>/git/reevo-ng/integrations/metricflow/definitions',
        'project_root': project_dir,
        'quoting': {},
        'models': {},
        'credentials': credentials,
    }

    return RuntimeConfig.from_args(config_dict)

def init_dbt(credentials: Dict[str, Any]) -> Any:
    """Initialize dbt adapter with given credentials."""
    # Reset any existing adapters
    reset_adapters()
    
    # Create config and register adapter
    config = create_dbt_config(credentials)
    register_adapter(config)
    
    # Return the adapter instance
    return get_adapter(config)

def main():
    # Example credentials for Postgres
    credentials = {
        'type': 'postgres',
        'host': 'localhost',
        'port': 5432,
        'user': 'your_user',
        'password': 'your_password',
        'database': 'your_database',
        'schema': 'public'
    }

    # Initialize dbt
    adapter = init_dbt(credentials)

    try:
        # Example: Execute a simple query
        results = adapter.execute(
            'SELECT current_timestamp as current_time',
            auto_begin=True,
            fetch=True
        )
        
        # Print results
        print("Query results:", results)

        # Example: Create a table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS test_table (
            id SERIAL PRIMARY KEY,
            name VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        adapter.execute(create_table_sql, auto_begin=True)
        
        # Example: Insert data
        insert_sql = """
        INSERT INTO test_table (name) VALUES ('test_record')
        """
        adapter.execute(insert_sql, auto_begin=True)

    finally:
        # Clean up adapter connections
        adapter.cleanup_connections()

if __name__ == "__main__":
    main()