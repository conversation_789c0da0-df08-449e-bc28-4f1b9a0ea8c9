import asyncio
import json

from elasticsearch import AsyncElasticsearch, Elasticsearch
from typing import Dict, Any, List


COBJECT_INDEX_NAME = "cobjects_3"

COBJECT_MAPPING = {
    "mappings": {
        "properties": {
            "id": {"type": "keyword"},
            "organization_id": {"type": "keyword"},
            "object_display_name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "cobject_metadata": {
                "type": "object",
                "properties": {
                    "organization_id": {"type": "keyword"},
                    "object_display_name": {"type": "text"},
                    "status": {"type": "keyword"},
                }
            },
            "attributes": {  # Single field for all attributes
                "type": "object",
                "dynamic": True  # Allows any JSON structure
            }
        }
    }
}

cobject_metadata = {
    "organization_id": "1",
    "object_display_name": "Enterprise Customer",
    "status": "active",
}

cobject_metadata_2 = {
    "organization_id": "2",
    "object_display_name": "Software License",
    "status": "disabled",
}

COBJECTS = [
    {
        "id": "1",
        "organization_id": "1",
        "object_display_name": "Enterprise Customer",
        "updated_at": "2024-01-15T12:30:00Z",
        "updated_by_user_id": "user_456",
        "cobject_metadata": cobject_metadata,
        "attributes": {
            "industry": "Technology",
            "tier": "Premium"
        }
    },
    {
        "id": "2",
        "organization_id": "1",
        "object_display_name": "Software License",
        "updated_at": "2024-02-10T15:45:00Z",
        "updated_by_user_id": "user_789",
        "cobject_metadata": cobject_metadata,
        "attributes": {
            "category": "Enterprise",
            "price_range": "10000-50000"
        }
    },
    {
        "id": "3",
        "organization_id": "2",
        "object_display_name": "Implementation Project",
        "updated_at": "2024-03-01T09:15:00Z",
        "updated_by_user_id": "user_123",
        "cobject_metadata": cobject_metadata_2,
        "attributes": {
            "complexity": "High",
            "duration": "6 months"
        }
    }
]


class ElasticsearchIndexManager:
    INDEX_MAPPINGS = {
        COBJECT_INDEX_NAME: COBJECT_MAPPING
    }

    def __init__(self, es_client: AsyncElasticsearch):
        self.client = es_client

    async def ensure_indices(self):
        """Ensure all required indices exist with proper mappings"""
        for index_name, mapping in self.INDEX_MAPPINGS.items():
            if not await self.client.indices.exists(index=index_name):
                await self.client.indices.create(
                    index=index_name,
                    body=mapping
                )


class ElasticsearchImportManager:
    def __init__(self, es_client: AsyncElasticsearch):
        self.client = es_client
        self.index_manager = ElasticsearchIndexManager(es_client)

    async def import_cobjects(self, cobjects: List[Dict[str, Any]]):
        """Import custom objects into Elasticsearch"""
        # Ensure indices exist
        await self.index_manager.ensure_indices()

        # Prepare bulk import operations
        operations = []
        for cobject in cobjects:
            operations.extend([
                {"index": {"_index": COBJECT_INDEX_NAME, "_id": cobject["id"]}},
                cobject
            ])

        if operations:
            # Execute bulk import
            await self.client.bulk(
                operations=operations, 
                refresh=True,
            )


class QuerySpec:
    pass

class ContactSearchService:
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client

    def _build_must_clauses(self, filters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        # Implement logic to build must clauses
        pass

    def _build_should_clauses(self, filters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        # Implement logic to build should clauses
        pass

    async def search_cobjects(self) -> List[Dict[str, Any]]:
        # Build Elasticsearch query
        must_clauses = [{
            "term": {
                "cobject_metadata.organization_id": "1"
            }
        }]

        # Add field-value search if provided
        field = "industry"
        value = "Technology"
        if field and value:
            must_clauses.append({
                "term": {
                    f"attributes.{field}": value
                }
            })

        query = {
            "query": {
                "bool": {
                    "must": must_clauses,
                    "should": [],
                    "filter": []
                }
            },
            "sort": [],
            "from": 0,
            "size": 10
        }

        results = await self.es_client.search(
            index=COBJECT_INDEX_NAME,
            body=query
        )

        return [hit["_source"] for hit in results["hits"]["hits"]]


async def test_elasticsearch():
    es_client = AsyncElasticsearch("http://localhost:9200")
    import_manager = ElasticsearchImportManager(es_client)
    await import_manager.import_cobjects(COBJECTS)
    search_service = ContactSearchService(es_client)
    results = await search_service.search_cobjects()
    print(json.dumps(results, indent=4))


if __name__ == "__main__":
    asyncio.run(test_elasticsearch())
