import asyncio

from elasticsearch import AsyncElasticsearch, Elasticsearch
from typing import Dict, Any

# from salestech_be.search.es.search.client_utils import get_es_search_client

from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from uuid import UUID


class AccountSearchDocument(BaseModel):
    """Denormalized account document for Elasticsearch"""
    account_id: UUID
    name: str
    last_updated_at: datetime
    updated_by_user_id: Optional[UUID]
    estimated_employee_count: Optional[int]
    estimated_annual_revenue: Optional[float]  # Using float instead of Decimal for ES compatibility
    categories: Optional[List[str]]

class AccountPrimaryFields(BaseModel):
    """Nested account fields for related documents"""
    name: str
    last_updated_at: datetime
    updated_by_user_id: Optional[UUID]
    estimated_employee_count: Optional[int]
    estimated_annual_revenue: Optional[float]
    categories: Optional[List[str]]

class ContactSearchDocument(BaseModel):
    """Denormalized contact document for Elasticsearch"""
    contact_id: UUID
    name: str
    last_updated_at: datetime
    updated_by_user_id: Optional[UUID]
    primary_account_id: Optional[UUID]
    primary_account: Optional[AccountPrimaryFields]

class MeetingFields(BaseModel):
    """Nested meeting fields for pipeline document"""
    title: str
    account_id: Optional[UUID]
    opportunity_id: Optional[UUID]
    attendee_contact_ids: List[UUID]
    attendee_user_ids: List[UUID]

class GlobalThreadFields(BaseModel):
    """Nested global thread fields for pipeline document"""
    subject: str
    last_updated_at: datetime
    updated_by_user_id: Optional[UUID]
    account_id: Optional[UUID]
    pipeline_id: Optional[UUID]

class PipelineSearchDocument(BaseModel):
    """Denormalized pipeline document for Elasticsearch"""
    pipeline_id: UUID
    name: str
    last_updated_at: datetime
    updated_by_user_id: Optional[UUID]
    
    # Account relationship
    account_id: Optional[UUID]
    account: Optional[AccountPrimaryFields]
    
    # Contact relationship
    primary_contact_id: Optional[UUID]
    primary_contact: Optional[ContactSearchDocument]
    
    # Next due task relationships
    next_due_task_account_id: Optional[UUID]
    next_due_task_account: Optional[AccountPrimaryFields]
    next_due_task_contact_ids: Optional[List[UUID]]
    next_due_task_meeting_id: Optional[UUID]
    next_due_task_meeting: Optional[MeetingFields]
    next_due_task_global_thread_id: Optional[UUID]
    next_due_task_global_thread: Optional[GlobalThreadFields]


ACCOUNT_INDEX_NAME = "accounts"
CONTACT_INDEX_NAME = "contacts"
PIPELINE_INDEX_NAME = "pipelines"
COBJECT_INDEX_NAME = "cobjects"

ACCOUNT_MAPPING = {
    "mappings": {
        "properties": {
            "account_id": {"type": "keyword"},
            "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "estimated_employee_count": {"type": "integer"},
            "estimated_annual_revenue": {"type": "float"},
            "categories": {"type": "keyword"}
        }
    }
}

CONTACT_MAPPING = {
    "mappings": {
        "properties": {
            "contact_id": {"type": "keyword"},
            "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "primary_account_id": {"type": "keyword"},
            "primary_account": {
                "type": "object",
                "properties": {
                    "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
                    "last_updated_at": {"type": "date"},
                    "updated_by_user_id": {"type": "keyword"},
                    "estimated_employee_count": {"type": "integer"},
                    "estimated_annual_revenue": {"type": "float"},
                    "categories": {"type": "keyword"}
                }
            }
        }
    }
}

PIPELINE_MAPPING = {
    "mappings": {
        "properties": {
            "pipeline_id": {"type": "keyword"},
            "name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "account": {
                "type": "object",
                "properties": {
                    "name": {"type": "text"},
                    "last_updated_at": {"type": "date"},
                    "estimated_employee_count": {"type": "integer"},
                    "estimated_annual_revenue": {"type": "float"},
                    "categories": {"type": "keyword"}
                }
            },
            "primary_contact": {
                "type": "object",
                "properties": {
                    "name": {"type": "text"},
                    "last_updated_at": {"type": "date"},
                    "primary_account": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "text"},
                            "estimated_employee_count": {"type": "integer"},
                            "estimated_annual_revenue": {"type": "float"},
                            "categories": {"type": "keyword"}
                        }
                    }
                }
            },
            "next_due_task_meeting": {
                "type": "object",
                "properties": {
                    "title": {"type": "text"},
                    "attendee_contact_ids": {"type": "keyword"},
                    "attendee_user_ids": {"type": "keyword"}
                }
            },
            "next_due_task_global_thread": {
                "type": "object",
                "properties": {
                    "subject": {"type": "text"},
                    "last_updated_at": {"type": "date"}
                }
            }
        }
    }
}

COBJECT_MAPPING = {
    "mappings": {
        "properties": {
            "cobject_id": {"type": "keyword"},
            "organization_id": {"type": "keyword"},
            "object_display_name": {"type": "text", "fields": {"keyword": {"type": "keyword"}}},
            "last_updated_at": {"type": "date"},
            "updated_by_user_id": {"type": "keyword"},
            "cobject_metadata_id": {"type": "keyword"},
            "cobject_metadata": {
                "type": "object",
                "properties": {
                    "organization_id": {"type": "keyword"},
                    "object_name": {"type": "text"},
                    "object_display_name": {"type": "text"},
                    "status": {"type": "keyword"},
                    "created_at": {"type": "date"},
                    "created_by_user_id": {"type": "keyword"},
                    "updated_at": {"type": "date"},
                    "updated_by_user_id": {"type": "keyword"},
                    "deleted_at": {"type": "date"},
                    "deleted_by_user_id": {"type": "keyword"},
                }
            },
            "value_1": {
                "type": "object",
                "properties": {
                    "field": {"type": "keyword"},
                    "value": {"type": "keyword"},
                }
            },
            "value_2": {
                "type": "object",
                "properties": {
                    "field": {"type": "keyword"},
                    "value": {"type": "keyword"},
                }
            }
        }
    }
}

cobject_metadata = {
    "organization_id": "1",
    "object_name": "Customer",
    "object_display_name": "Enterprise Customer",
    "status": "active",
}

COBJECTS = [
    {
        "id": "1",
        "organization_id": "1",
        "object_name": "Customer",
        "object_display_name": "Enterprise Customer",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "created_by_user_id": "user_123",
        "updated_at": "2024-01-15T12:30:00Z",
        "updated_by_user_id": "user_456",
        "deleted_at": None,
        "deleted_by_user_id": None,
        "value_1": {
            "field": "industry",
            "value": "Technology"
        },
        "value_2": {
            "field": "tier",
            "value": "Premium"
        }
    },
    {
        "id": "2",
        "organization_id": "1",
        "object_name": "Product",
        "object_display_name": "Software License",
        "status": "active",
        "created_at": "2024-02-01T00:00:00Z",
        "created_by_user_id": "user_123",
        "updated_at": "2024-02-10T15:45:00Z",
        "updated_by_user_id": "user_789",
        "deleted_at": None,
        "deleted_by_user_id": None,
        "value_1": {
            "field": "category",
            "value": "Enterprise"
        },
        "value_2": {
            "field": "price_range",
            "value": "10000-50000"
        }
    },
    {
        "id": "3",
        "organization_id": "2",
        "object_name": "Project",
        "object_display_name": "Implementation Project",
        "status": "archived",
        "created_at": "2023-12-01T00:00:00Z",
        "created_by_user_id": "user_456",
        "updated_at": "2024-03-01T09:15:00Z",
        "updated_by_user_id": "user_123",
        "deleted_at": "2024-03-01T09:15:00Z",
        "deleted_by_user_id": "user_123",
        "value_1": {
            "field": "complexity",
            "value": "High"
        },
        "value_2": {
            "field": "duration",
            "value": "6 months"
        }
    }
]


class ElasticsearchIndexManager:
    INDEX_MAPPINGS = {
        # ACCOUNT_INDEX_NAME: ACCOUNT_MAPPING,
        # CONTACT_INDEX_NAME: CONTACT_MAPPING,
        # PIPELINE_INDEX_NAME: PIPELINE_MAPPING,
        COBJECT_INDEX_NAME: COBJECT_MAPPING
    }

    def __init__(self, es_client: AsyncElasticsearch):
        self.client = es_client

    async def ensure_indices(self):
        """Ensure all required indices exist with proper mappings"""
        for index_name, mapping in self.INDEX_MAPPINGS.items():
            if not await self.client.indices.exists(index=index_name):
                await self.client.indices.create(
                    index=index_name,
                    body=mapping
                )


class ElasticsearchImportManager:
    def __init__(self, es_client: AsyncElasticsearch):
        self.client = es_client
        self.index_manager = ElasticsearchIndexManager(es_client)

    async def import_cobjects(self, cobjects: List[Dict[str, Any]]):
        """Import custom objects into Elasticsearch"""
        # Ensure indices exist
        await self.index_manager.ensure_indices()

        # Prepare bulk import operations
        operations = []
        for cobject in cobjects:
            operations.extend([
                {"index": {"_index": COBJECT_INDEX_NAME, "_id": cobject["id"]}},
                {
                    "cobject_metadata": {
                        "organization_id": cobject["organization_id"],
                        "object_name": cobject["object_name"],
                        "object_display_name": cobject["object_display_name"],
                        "status": cobject["status"],
                        "created_at": cobject["created_at"],
                        "created_by_user_id": cobject["created_by_user_id"],
                        "updated_at": cobject["updated_at"],
                        "updated_by_user_id": cobject["updated_by_user_id"],
                        "deleted_at": cobject.get("deleted_at"),
                        "deleted_by_user_id": cobject.get("deleted_by_user_id")
                    },
                    "value_1": {
                        "field": cobject.get("value_1_field"),
                        "value": cobject.get("value_1_value")
                    },
                    "value_2": {
                        "field": cobject.get("value_2_field"), 
                        "value": cobject.get("value_2_value")
                    }
                }
            ])

        if operations:
            # Execute bulk import
            await self.client.bulk(
                operations=operations, 
                refresh=True,
            )

    async def import_accounts(self, accounts: List[Dict[str, Any]]):
        """Import accounts into Elasticsearch"""
        await self.index_manager.ensure_indices()

        operations = []
        for account in accounts:
            operations.extend([
                {"index": {"_index": ElasticsearchIndexManager.ACCOUNT_INDEX_NAME, "_id": str(account["account_id"])}},
                {
                    "account_id": str(account["account_id"]),
                    "name": account["name"],
                    "last_updated_at": account["last_updated_at"],
                    "updated_by_user_id": str(account["updated_by_user_id"]) if account.get("updated_by_user_id") else None,
                    "estimated_employee_count": account.get("estimated_employee_count"),
                    "estimated_annual_revenue": account.get("estimated_annual_revenue"),
                    "categories": account.get("categories", [])
                }
            ])

        if operations:
            await self.client.bulk(operations, refresh=True)

    async def import_contacts(self, contacts: List[Dict[str, Any]]):
        """Import contacts into Elasticsearch"""
        await self.index_manager.ensure_indices()

        operations = []
        for contact in contacts:
            operations.extend([
                {"index": {"_index": ElasticsearchIndexManager.CONTACT_INDEX_NAME, "_id": str(contact["contact_id"])}},
                {
                    "contact_id": str(contact["contact_id"]),
                    "name": contact["name"],
                    "last_updated_at": contact["last_updated_at"],
                    "updated_by_user_id": str(contact["updated_by_user_id"]) if contact.get("updated_by_user_id") else None,
                    "primary_account_id": str(contact["primary_account_id"]) if contact.get("primary_account_id") else None,
                    "primary_account": contact.get("primary_account")
                }
            ])

        if operations:
            await self.client.bulk(operations, refresh=True)


# from core.services.contact_service import ContactService
# from core.services.account_service import AccountService
# from core.services.email_service import EmailService
# from core.search.models import ContactSearchDocument

# class ElasticsearchSyncService:
#     def __init__(
#         self,
#         es_client: AsyncElasticsearch,
#         contact_service: ContactService,
#         account_service: AccountService,
#         email_service: EmailService,
#     ):
#         self.es_client = es_client
#         self.contact_service = contact_service
#         self.account_service = account_service
#         self.email_service = email_service

#     async def build_contact_document(self, contact_id: UUID) -> ContactSearchDocument:
#         """Build a denormalized contact document"""
#         # Fetch all related data
#         contact = await self.contact_service.get_contact(contact_id)
#         emails = await self.email_service.list_contact_emails(contact_id)
#         accounts = await self.account_service.list_contact_accounts(contact_id)

#         # Calculate computed fields
#         total_revenue = sum(account.revenue for account in accounts)
#         email_count = len(emails)
#         last_interaction = max(
#             (email.last_activity for email in emails if email.last_activity),
#             default=None
#         )

#         return ContactSearchDocument(
#             contact_id=contact.id,
#             first_name=contact.first_name,
#             last_name=contact.last_name,
#             title=contact.title,
#             company=contact.company,
#             created_at=contact.created_at,
#             updated_at=contact.updated_at,
#             emails=[EmailDocument.from_orm(email) for email in emails],
#             accounts=[AccountDocument.from_orm(account) for account in accounts],
#             total_revenue=total_revenue,
#             email_count=email_count,
#             last_interaction_date=last_interaction
#         )

#     async def sync_contact(self, contact_id: UUID):
#         """Sync a single contact to Elasticsearch"""
#         document = await self.build_contact_document(contact_id)
#         await self.es_client.index(
#             index="contacts",
#             id=str(contact_id),
#             body=document.dict(),
#             refresh=True
#         )

#     async def bulk_sync_contacts(self, contact_ids: List[UUID], batch_size: int = 100):
#         """Sync multiple contacts in bulk"""
#         actions = []
#         for i in range(0, len(contact_ids), batch_size):
#             batch = contact_ids[i:i + batch_size]
#             for contact_id in batch:
#                 document = await self.build_contact_document(contact_id)
#                 actions.append({
#                     "_index": "contacts",
#                     "_id": str(contact_id),
#                     "_source": document.dict()
#                 })
            
#             await self.es_client.bulk(body=actions)
#             actions = []

class QuerySpec:
    pass

class ContactSearchService:
    def __init__(self, es_client: AsyncElasticsearch):
        self.es_client = es_client

    def _build_must_clauses(self, filters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        # Implement logic to build must clauses
        pass

    def _build_should_clauses(self, filters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        # Implement logic to build should clauses
        pass

    async def search_cobjects(self, query_spec: QuerySpec) -> List[Dict[str, Any]]:
        # Build Elasticsearch query
        query = {
            "query": {
                "bool": {
                    "must": [{
                        "term": {
                            "metadata.organization_id": "1"
                        }
                    }],
                    "should": [],
                    "filter": []
                }
            },
            "sort": [],
            "from": 0,
            "size": 10
        }

        results = await self.es_client.search(
            index=COBJECT_INDEX_NAME,
            body=query
        )

        return [hit["_source"] for hit in results["hits"]["hits"]]

    async def search_contacts(self, query_spec: QuerySpec) -> List[Dict[str, Any]]:
        # Build Elasticsearch query
        query = {
            "query": {
                "bool": {
                    "must": self._build_must_clauses(query_spec.filters),
                    "should": self._build_should_clauses(query_spec.filters),
                    "filter": self._build_filter_clauses(query_spec.filters)
                }
            },
            "sort": self._build_sort(query_spec.sort_by, query_spec.sort_desc),
            "from": (query_spec.page - 1) * query_spec.page_size,
            "size": query_spec.page_size
        }

        # Add nested queries if needed
        if self._has_email_filters(query_spec.filters):
            query["query"]["bool"]["must"].append({
                "nested": {
                    "path": "emails",
                    "query": self._build_email_query(query_spec.filters)
                }
            })

        if self._has_account_filters(query_spec.filters):
            query["query"]["bool"]["must"].append({
                "nested": {
                    "path": "accounts",
                    "query": self._build_account_query(query_spec.filters)
                }
            })

        results = await self.es_client.search(
            index=COBJECT_INDEX_NAME,
            body=query
        )

        return [hit["_source"] for hit in results["hits"]["hits"]]


async def test_elasticsearch():
    # es_client = get_es_search_client()
    es_client = AsyncElasticsearch("http://localhost:9200")
    index_manager = ElasticsearchIndexManager(es_client)
    await index_manager.ensure_indices()
    # print(await es_client.info())

    import_manager = ElasticsearchImportManager(es_client)
    cobjects = [
        {
            "id": "1",
            "organization_id": "1",
            "object_name": "Test Object",
            "object_display_name": "Test Object",
            "status": "active",
            "created_at": "2021-01-01",
            "created_by_user_id": "1",
            "updated_at": "2021-01-01",
            "updated_by_user_id": "1",
            "deleted_at": None,
            "deleted_by_user_id": None,
            "value_1_field": "name",
            "value_1_value": "Test Object",
            "value_2_field": "description",
            "value_2_value": "This is a test object"
        }
    ]
    await import_manager.import_cobjects(cobjects)

    search_service = ContactSearchService(es_client)
    query_spec = QuerySpec()
    results = await search_service.search_cobjects(query_spec)
    print(results)


if __name__ == "__main__":
    asyncio.run(test_elasticsearch())
