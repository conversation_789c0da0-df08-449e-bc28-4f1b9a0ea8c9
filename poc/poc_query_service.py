import asyncio
import datetime
import os
from typing import Any
import psycopg
from uuid import UUID
import jinja2
from pydantic import BaseModel


class Filter(BaseModel):
    col: str
    op: str
    val: Any
    col_alias: str | None = None

    def __str__(self):
        return self.to_where_clause()
    
    def __repr__(self):
        return self.__str__()

    def to_where_clause(self, col_alias: str = None):
        """Convert the filter to a SQL WHERE clause.
        
        Returns:
            str: A SQL WHERE clause fragment representing this filter.
        """
        col_name = col_alias or self.col
        # Handle different operators and value types
        if self.op in ('=', '!=', '>', '<', '>=', '<='):
            # For None values, convert to IS NULL or IS NOT NULL
            if self.val is None:
                if self.op == '=':
                    return f"{col_name} IS NULL"
                elif self.op == '!=':
                    return f"{col_name} IS NOT NULL"
                else:
                    raise ValueError(f"Invalid operator '{self.op}' for NULL value")

            # For other types (numbers, booleans), use as is
            elif isinstance(self.val, (int, float, bool)):
                return f"{col_name} {self.op} {self.val}"

            # For string values, add quotes
            elif isinstance(self.val, (str, UUID, datetime.datetime, datetime.date)):
                return f"{col_name} {self.op} '{str(self.val).replace("'", "\'")}'"

            else:
                raise ValueError(f"Unsupported value type: {type(self.val)}")

        elif self.op.upper() == 'IN':
            # Handle IN operator with list of values
            if not isinstance(self.val, (list, tuple)):
                raise ValueError("Value for IN operator must be a list or tuple")
            
            # Format each value based on its type
            formatted_values = []
            for v in self.val:
                if isinstance(v, (str, UUID, datetime.datetime, datetime.date)):
                    formatted_values.append(f"'{str(v).replace("'", "\'")}'")
                elif v is None:
                    formatted_values.append("NULL")
                else:
                    formatted_values.append(str(v))
            
            values_str = ", ".join(formatted_values)
            return f"{col_name} IN ({values_str})"
        
        elif self.op.upper() == 'LIKE':
            if not isinstance(self.val, str):
                raise ValueError("Value for LIKE operator must be a string")
            return f"{col_name} LIKE '{str(self.val).replace("'", "\'")}'"

        else:
            raise ValueError(f"Unsupported operator: {self.op}")


class Metric(BaseModel):
    col: str
    aggregate: str
    alias: str | None = None

    def __str__(self):
        return self.to_select_clause()
    
    def __repr__(self):
        return self.__str__()

    def to_select_clause(self):
        if self.alias:
            return f"{self.aggregate}({self.col}) AS {self.alias}"
        else:
            return f"{self.aggregate}({self.col})"


class ReportingQueryService:
    async def run_sql(
        self,
        sql: str,
    ) -> list[dict[str, Any]]:
        """Execute a SQL query against the materialize database.

        Args:
            sql: The SQL query to execute
            params: Optional query parameters

        Returns:
            List of dictionaries containing the query results
        """
        # temp hard coded dsn definition only in POC code
        # do not use in Production Code
        dsn_dev = "user=<EMAIL> password=mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9 host=c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud port=6875 dbname=mz_db_reevo_main_dev sslmode=require"
        async with await psycopg.AsyncConnection.connect(dsn_dev) as aconn, aconn.cursor() as acur:
            await acur.execute(sql)
            return await acur.fetchall()

    def generate_sql_by_table(
        self,
        *,
        organization_id: UUID,
        table_name: str,
        datasource_columns: list[str],
        columns: list[str],
        filters: list[str],
        metrics: list[str],
        dimensions: list[str],
        order_by: list[str],
        limit: int,
    ) -> str:
        """Generate a SQL query from a table.
        """
        if columns:
            columns = [c for c in columns if c in datasource_columns]
        else:
            columns = datasource_columns
        
        dimensions = [d for d in dimensions if d in datasource_columns]
        filters = [f for f in filters if f.col in datasource_columns]

        # Build the base SELECT clause
        if dimensions and metrics:
            select_clause = "SELECT " + ", ".join(str(v) for v in dimensions + metrics)
        elif columns:
            select_clause = "SELECT " + ", ".join(columns)
        else:
            select_clause = "SELECT *"

        # Add the FROM clause with the table name
        from_clause = f"\nFROM {table_name}"

        # Add any WHERE filters
        organization_filter = Filter(col="organization_id", op="=", val=organization_id)
        filters.append(organization_filter)
        where_clause = "\nWHERE " + " AND ".join(str(f) for f in filters)

        # Add any GROUP BY clauses
        group_by_clause = "\nGROUP BY " + ", ".join(dimensions) if dimensions else ""

        # Add ORDER BY
        order_clause = "\nORDER BY " + ", ".join(order_by) if order_by else ""

        # Add LIMIT
        limit_clause = f"\nLIMIT {limit}" if limit else ""

        # Combine all clauses
        sql = select_clause + from_clause + where_clause + group_by_clause + order_clause + limit_clause

        return sql

    def generate_sql_by_template(
        self,
        *,
        organization_id: UUID,
        template: str,
        datasource_columns: list[str],
        columns: list[str],
        filters: list[str],
        metrics: list[str],
        dimensions: list[str],
        order_by: list[str],
        limit: int,
    ) -> str:
        """Generate a SQL query from a template.

        Args:
            sql: The SQL query to execute
            params: Optional query parameters

        Returns:
            List of dictionaries containing the query results
        """

        # sql = "SELECT * FROM fct_enriched_pipeline p where {{ filter_values('id', 'p.id') }} AND {{ filter_values('owner_user_id', 'p.owner_user_id') }}"
        # sql = "SELECT * FROM fct_enriched_pipeline p where 1=1 {{ and_filter_values('id', 'p.id') }} {{ and_filter_values('owner_user_id', 'p.owner_user_id') }}"
        # sql = "SELECT * FROM fct_enriched_pipeline p where 1<>1 {{ or_filter_values('id', 'p.id') }} {{ or_filter_values('owner_user_id', 'p.owner_user_id') }}"
        # sql = "SELECT * FROM fct_enriched_pipeline p where  AND p.owner_user_id IN ('1000-10000', '10000-20000')"
        
        def filter_values(col, col_alias=None, remove_filter: bool = False):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return None
            conditions = []
            for m in matches:
                conditions.append(m.to_where_clause(col_alias=col_alias))
            return ' AND '.join(conditions)

        def and_filter_values(col, col_alias=None):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return ''
            conditions = []
            for m in matches:
                conditions.append('AND ' + m.to_where_clause(col_alias=col_alias))
            return '\n'.join(conditions)

        def or_filter_values(col, col_alias=None):
            matches = [f for f in filters if f.col == col]
            if not matches:
                return ''
            conditions = []
            for m in matches:
                conditions.append('OR ' + m.to_where_clause(col_alias=col_alias))
            return '\n'.join(conditions)

        template = jinja2.Template(template)
        template.globals['filter_values'] = filter_values
        template.globals['and_filter_values'] = and_filter_values
        template.globals['or_filter_values'] = or_filter_values
        inner_sql = template.render()
        inner_sql = inner_sql.strip().rstrip(';')

        return self.generate_sql_by_table(
            organization_id=organization_id,
            table_name=f"({inner_sql}\n) AS virtual_table",
            datasource_columns=datasource_columns,
            columns=columns,
            filters=filters,
            metrics=metrics,
            dimensions=dimensions,
            order_by=order_by,
            limit=limit,
        )

    async def query_by_template(
        self,
        *,
        organization_id: UUID,
        template_name: str,
        datasource_columns: list[str],
        columns: list[str],
        filters: list[str],
        metrics: list[str],
        dimensions: list[str],
        order_by: list[str],
        limit: int,
    ) -> list[dict[str, Any]]:
        """Query a dbt model.
        """
        # template file path by absolute path only in POC code
        # do not use in Production Code
        current_dir = os.path.dirname(os.path.abspath(__file__))
        with open(f"{current_dir}/dataset/{template_name}.sql") as f:
            template = f.read()
        sql = self.generate_sql_by_template(
            organization_id=organization_id,
            template=template,
            datasource_columns=datasource_columns,
            columns=columns,
            filters=filters,
            metrics=metrics,
            dimensions=dimensions,
            order_by=order_by,
            limit=limit
        )
        # print(sql)
        # quit()
        return await self.run_sql(sql)

    async def query_by_dbt_model(
        self,
        *,
        organization_id: UUID,
        dbt_model: str,
        datasource_columns: list[str],
        columns: list[str],
        filters: list[str],
        metrics: list[str],
        dimensions: list[str],
        order_by: list[str],
        limit: int,
    ) -> list[dict[str, Any]]:
        """Query a dbt model.
        """
        sql = self.generate_sql_by_table(
            organization_id=organization_id,
            table_name=dbt_model,
            datasource_columns=datasource_columns,
            columns=columns,
            filters=filters,
            metrics=metrics,
            dimensions=dimensions,
            order_by=order_by,
            limit=limit
        )
        # print(sql)
        # quit()
        return await self.run_sql(sql)



async def test_query_by_dbt_model():
    query_service = ReportingQueryService()
    organization_id = UUID("c6734584-b3f3-4608-b62e-a993f4703a8e")

    datasource_columns = ["id", "owner_user_id", "amount", "mapped_pipeline_stage_id", "mapped_pipeline_stage_name"]
    columns = []
    filters = [
        Filter(col="owner_user_id", op="in", val=["36ad3654-0978-41f6-b25d-51915d47a401"]),
        Filter(col="some_column_not_exists", op="<=", val=12000),
    ]
    metrics = [
        Metric(col="id", aggregate="count", alias="pipeline_count"),
        Metric(col="amount", aggregate="sum", alias="pipeline_value"),
    ]
    dimensions = ["mapped_pipeline_stage_id", "mapped_pipeline_stage_name"]
    order_by = ["mapped_pipeline_stage_id", "mapped_pipeline_stage_name"]
    limit = 100

    print(await query_service.query_by_dbt_model(
        organization_id=organization_id,
        dbt_model="fct_enriched_pipeline",
        datasource_columns=datasource_columns,
        columns=columns,
        filters=filters,
        metrics=metrics,
        dimensions=dimensions,
        order_by=order_by,
        limit=limit,
    ))


async def test_query_by_template():
    query_service = ReportingQueryService()
    organization_id = UUID("c6734584-b3f3-4608-b62e-a993f4703a8e")

    datasource_columns = ["bucket_range_start", "bucket_range_end", "organization_id", "pipeline_count", "pipeline_value"]
    columns = ["bucket_range_start", "bucket_range_end", "pipeline_count"]
    filters = [
        Filter(col="owner_user_id", op="in", val=["36ad3654-0978-41f6-b25d-51915d47a401"]),
        Filter(col="pipeline_amount", op=">=", val=1000),
        Filter(col="contact_count", op="=", val=1),
        Filter(col="some_column_not_exists", op="<=", val=12000),
    ]
    metrics = []
    dimensions = []
    order_by = []
    limit = 100
    print(await query_service.query_by_template(
        organization_id=organization_id,
        template_name="pipeline_deal_bucketed_count_value",
        datasource_columns=datasource_columns,
        columns=columns,
        filters=filters,
        metrics=metrics,
        dimensions=dimensions,
        order_by=order_by,
        limit=limit,
    ))


async def main():
    await test_query_by_dbt_model()
    await test_query_by_template()

asyncio.run(main())
