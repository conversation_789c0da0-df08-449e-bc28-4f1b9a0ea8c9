import asyncio
import asyncpg

async def run():
    conn = await asyncpg.connect(
        user='<EMAIL>',
        password='mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9',
        database='mz_db_reevo_main_dev',
        host='c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud',
        port=6875,
        ssl=True
    )
    values = await conn.fetch(
        'SELECT * FROM fct_enriched_pipeline limit 10',
    )
    print(values)
    await conn.close()


async def run2():
    conn = await asyncpg.connect(
        user='salestech_be',
        password='salestech_be',
        database='salestech_be',
        host='localhost',
        port=5432,
        ssl=False
    )
    values = await conn.fetch(
        'SELECT * FROM pipeline limit 10',
    )
    print(values)
    await conn.close()

asyncio.run(run2())