import asyncio

from sqlalchemy import event
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.orm import Session

engine = create_async_engine("postgresql+psycopg://salestech_be:salestech_be@localhost:5432/salestech_be")
engine = create_async_engine("postgresql+psycopg_async://<EMAIL>:<EMAIL>:6875/mz_db_reevo_main_dev?sslmode=require")


async def go():
    async with engine.connect() as conn:
        try:
            result = await conn.execute(text("select * from fct_enriched_pipeline limit 10"))
            print(result.fetchall())
        except Exception as e:
            print(f"An error occurred: {e}")
            # No explicit rollback needed, the context manager handles it


asyncio.run(go())