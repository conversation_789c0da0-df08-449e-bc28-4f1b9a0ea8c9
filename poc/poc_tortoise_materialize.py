from datetime import <PERSON><PERSON><PERSON>
from tortoise import Tortoise, connections
from itertools import pairwise

async def init():
    await <PERSON><PERSON>ise.init(
        # db_url='asyncpg://salestech_be:salestech_be@localhost:5432/salestech_be',
        # db_url='asyncpg://<EMAIL>:<EMAIL>:6875/mz_db_reevo_main_dev?ssl=require',
        db_url='psycopg://<EMAIL>:<EMAIL>:6875/mz_db_reevo_main_dev?ssl=require',
        # dsn_dev = "user=<EMAIL> password=mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9 host=c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud port=6875 dbname=mz_db_reevo_main_dev sslmode=require",
        modules={'models': ['models']}
    )


async def fix_pipeline_tracking():
    conn = connections.get("default")

    sql = """
        SELECT * from fct_enriched_pipeline limit 10
    """
    result = await conn.execute_query_dict(sql)
    print(result)


async def main():
    await init()
    await fix_pipeline_tracking()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
