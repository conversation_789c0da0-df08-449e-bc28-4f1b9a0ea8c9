#!/usr/bin/env python3

import asyncio
import psycopg

dsn_local = "user=materialize password=materialize host=localhost port=6875 dbname=materialize sslmode=disable"
dsn_dev = "user=<EMAIL> password=mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9 host=c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud port=6875 dbname=mz_db_reevo_main_dev sslmode=require"

async def run():
    async with await psycopg.AsyncConnection.connect(dsn_dev) as aconn:
        async with aconn.cursor() as acur:
            await acur.execute("SELECT * FROM fct_enriched_pipeline limit 10")
            async for record in acur:
                print(record)


async def run2():
    async with await psycopg.AsyncConnection.connect(dsn_local) as aconn:
        async with aconn.cursor() as acur:
            await acur.execute("SELECT * FROM mv_account limit 10")
            async for record in acur:
                print(record)

asyncio.run(run2())
