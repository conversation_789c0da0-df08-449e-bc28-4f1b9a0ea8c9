#!/usr/bin/env python3

import psycopg2
import sys

dsn_local = "user=materialize password=materialize host=localhost port=6875 dbname=materialize sslmode=disable"
dsn_dev = "user=<EMAIL> password=mzp_05a4894a65e1418792f6e3594ecfb8d849b92c4dde3c4e289f4792092d2c79e9 host=c87i8hd8mkmx0djk0b9qk6w2n.lb.us-west-2.aws.materialize.cloud port=6875 dbname=mz_db_reevo_main_dev sslmode=require"
dsn = dsn_local

conn = psycopg2.connect(dsn)

with conn.cursor() as cur:
    cur.execute("SELECT * FROM fct_enriched_pipeline limit 10;")
    for row in cur:
        print(row)