{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.9.2", "generated_at": "2025-02-25T11:45:31.061495Z", "invocation_id": "d54c12d9-6b68-4a78-9fd9-68009589861b", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:30.881355Z", "completed_at": "2025-02-25T11:45:30.901499Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:30.901687Z", "completed_at": "2025-02-25T11:45:30.949656Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.06907916069030762, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.mf_tutorial_project.all_days", "compiled": true, "compiled_code": "\n\nwith days as (\n    -- Only generate dates for 2022 since that's what's in the seed data.\n    -- Note that `date_spine` does not include the end date.\n    \n\n\n    \n\n\n    with rawdata as (\n\n        \n\n    \n\n    with p as (\n        select 0 as generated_number union all select 1\n    ), unioned as (\n\n    select\n\n    \n    p0.generated_number * power(2, 0)\n     + \n    \n    p1.generated_number * power(2, 1)\n     + \n    \n    p2.generated_number * power(2, 2)\n     + \n    \n    p3.generated_number * power(2, 3)\n     + \n    \n    p4.generated_number * power(2, 4)\n     + \n    \n    p5.generated_number * power(2, 5)\n     + \n    \n    p6.generated_number * power(2, 6)\n     + \n    \n    p7.generated_number * power(2, 7)\n     + \n    \n    p8.generated_number * power(2, 8)\n    \n    \n    + 1\n    as generated_number\n\n    from\n\n    \n    p as p0\n     cross join \n    \n    p as p1\n     cross join \n    \n    p as p2\n     cross join \n    \n    p as p3\n     cross join \n    \n    p as p4\n     cross join \n    \n    p as p5\n     cross join \n    \n    p as p6\n     cross join \n    \n    p as p7\n     cross join \n    \n    p as p8\n    \n    \n\n    )\n\n    select *\n    from unioned\n    where generated_number <= 365\n    order by generated_number\n\n\n\n    ),\n\n    all_periods as (\n\n        select (\n            \n\n    date_add(make_date(2022, 1, 1), interval (row_number() over (order by 1) - 1) day)\n\n\n        ) as date_day\n        from rawdata\n\n    ),\n\n    filtered as (\n\n        select *\n        from all_periods\n        where date_day <= make_date(2023, 1, 1)\n\n    )\n\n    select * from filtered\n\n\n\n),\n\nfinal as (\n    select cast(date_day as date) as date_day\n    from days\n)\n\n\nselect * from final\nwhere date_day >= DATE '2022-01-01'\nand date_day  < DATE '2023-01-01'", "relation_name": "\"mf_tutorial\".\"main\".\"all_days\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:30.952570Z", "completed_at": "2025-02-25T11:45:30.954369Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:30.954540Z", "completed_at": "2025-02-25T11:45:30.972932Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.021052837371826172, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.mf_tutorial_project.countries", "compiled": true, "compiled_code": "select\n    *\nfrom \"mf_tutorial\".\"main\".\"countries_seed\"", "relation_name": "\"mf_tutorial\".\"main\".\"countries\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:30.975030Z", "completed_at": "2025-02-25T11:45:30.976723Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:30.976896Z", "completed_at": "2025-02-25T11:45:30.986005Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.01159811019897461, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.mf_tutorial_project.customers", "compiled": true, "compiled_code": "select\n    *\nfrom \"mf_tutorial\".\"main\".\"customers_seed\"", "relation_name": "\"mf_tutorial\".\"main\".\"customers\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:30.988021Z", "completed_at": "2025-02-25T11:45:30.989762Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:30.989936Z", "completed_at": "2025-02-25T11:45:30.999408Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.012006759643554688, "adapter_response": {"_message": "OK"}, "message": "OK", "failures": null, "unique_id": "model.mf_tutorial_project.transactions", "compiled": true, "compiled_code": "select\n    *\nfrom \"mf_tutorial\".\"main\".\"transactions_seed\"", "relation_name": "\"mf_tutorial\".\"main\".\"transactions\"", "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:31.001182Z", "completed_at": "2025-02-25T11:45:31.001184Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:31.001332Z", "completed_at": "2025-02-25T11:45:31.034246Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.03362464904785156, "adapter_response": {"_message": "INSERT 6", "code": "INSERT", "rows_affected": 6}, "message": "INSERT 6", "failures": null, "unique_id": "seed.mf_tutorial_project.countries_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:31.036113Z", "completed_at": "2025-02-25T11:45:31.036115Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:31.036271Z", "completed_at": "2025-02-25T11:45:31.044168Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.008692264556884766, "adapter_response": {"_message": "INSERT 4", "code": "INSERT", "rows_affected": 4}, "message": "INSERT 4", "failures": null, "unique_id": "seed.mf_tutorial_project.customers_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2025-02-25T11:45:31.045966Z", "completed_at": "2025-02-25T11:45:31.045967Z"}, {"name": "execute", "started_at": "2025-02-25T11:45:31.046122Z", "completed_at": "2025-02-25T11:45:31.057477Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.012113094329833984, "adapter_response": {"_message": "INSERT 50", "code": "INSERT", "rows_affected": 50}, "message": "INSERT 50", "failures": null, "unique_id": "seed.mf_tutorial_project.transactions_seed", "compiled": null, "compiled_code": null, "relation_name": null, "batch_results": null}], "elapsed_time": 0.29953980445861816, "args": {"use_colors_file": true, "defer": false, "state_modified_compare_vars": false, "partial_parse": true, "use_colors": true, "project_dir": "/Users/<USER>/git/reevo-ng/mf_tutorial_project", "log_format": "default", "macro_debugging": false, "profiles_dir": "/Users/<USER>/git/reevo-ng/mf_tutorial_project", "favor_state": false, "include_saved_query": false, "skip_nodes_if_on_run_start_fails": false, "empty": false, "export_saved_queries": false, "invocation_command": "dbt build", "partial_parse_file_diff": true, "require_batched_execution_for_custom_microbatch_strategy": false, "log_level_file": "debug", "require_explicit_package_overrides_for_builtin_materializations": true, "send_anonymous_usage_stats": true, "write_json": true, "log_path": "/Users/<USER>/git/reevo-ng/mf_tutorial_project/logs", "warn_error_options": {"include": [], "exclude": []}, "print": true, "populate_cache": true, "printer_width": 80, "exclude": [], "require_nested_cumulative_type_params": false, "resource_types": [], "cache_selected_only": false, "vars": {}, "version_check": true, "log_file_max_bytes": 10485760, "state_modified_compare_more_unrendered_values": false, "log_format_file": "debug", "which": "build", "indirect_selection": "eager", "require_resource_names_without_spaces": false, "select": [], "show": false, "static_parser": true, "introspect": true, "quiet": false, "show_resource_report": false, "exclude_resource_types": [], "require_yaml_configuration_for_mf_time_spines": false, "log_level": "info", "strict_mode": false, "source_freshness_run_project_hooks": false}}