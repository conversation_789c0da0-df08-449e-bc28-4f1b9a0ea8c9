{"semantic_models": [{"name": "customers", "defaults": {"agg_time_dimension": "ds"}, "description": "Location and description of all customers.", "node_relation": {"alias": "customers", "schema_name": "main", "database": "mf_tutorial", "relation_name": "\"mf_tutorial\".\"main\".\"customers\""}, "primary_entity": null, "entities": [{"name": "customer", "description": null, "type": "primary", "role": null, "expr": "id_customer", "metadata": null, "label": null}, {"name": "country", "description": null, "type": "foreign", "role": null, "expr": null, "metadata": null, "label": null}], "measures": [{"name": "new_customers", "agg": "sum", "description": null, "create_metric": false, "expr": "1", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}], "dimensions": [{"name": "ds", "description": null, "type": "time", "is_partition": false, "type_params": {"time_granularity": "day", "validity_params": null}, "expr": null, "metadata": null, "label": null}, {"name": "customer_country", "description": null, "type": "categorical", "is_partition": false, "type_params": null, "expr": "country", "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}, {"name": "countries", "defaults": null, "description": "Regions mapped to countries.", "node_relation": {"alias": "countries", "schema_name": "main", "database": "mf_tutorial", "relation_name": "\"mf_tutorial\".\"main\".\"countries\""}, "primary_entity": null, "entities": [{"name": "country", "description": null, "type": "primary", "role": null, "expr": null, "metadata": null, "label": null}], "measures": [], "dimensions": [{"name": "region", "description": "The region associated with the country.", "type": "categorical", "is_partition": false, "type_params": null, "expr": null, "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}, {"name": "transactions", "defaults": {"agg_time_dimension": "ds"}, "description": "Each row represents one transaction. There\nwill be a new row for any cancellations or alterations.\nThere is a transaction, order, and customer id for\nevery transaction. There is only one transaction id per\ntransaction, but there can be many rows per order id and\ncustomer id. The `ds` or date is reflected in UTC.\n", "node_relation": {"alias": "transactions", "schema_name": "main", "database": "mf_tutorial", "relation_name": "\"mf_tutorial\".\"main\".\"transactions\""}, "primary_entity": null, "entities": [{"name": "transaction", "description": null, "type": "primary", "role": null, "expr": "id_transaction", "metadata": null, "label": null}, {"name": "customer", "description": null, "type": "foreign", "role": null, "expr": "id_customer", "metadata": null, "label": null}, {"name": "id_order", "description": null, "type": "foreign", "role": null, "expr": null, "metadata": null, "label": null}], "measures": [{"name": "transaction_amount_usd", "agg": "sum", "description": "The total USD value of the transaction.", "create_metric": false, "expr": null, "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "transactions", "agg": "sum", "description": "The total number of transactions.", "create_metric": false, "expr": "1", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "quick_buy_amount_usd", "agg": "sum", "description": "The total USD value of the transactions that were purchased using the \"quick buy\" button.", "create_metric": false, "expr": "CASE WHEN transaction_type_name = 'quick-buy' THEN transaction_amount_usd ELSE 0 END", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "quick_buy_transactions", "agg": "sum_boolean", "description": "The total transactions bought as quick buy.", "create_metric": false, "expr": "CASE WHEN transaction_type_name = 'quick-buy' THEN TRUE ELSE FALSE END", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "cancellations_usd", "agg": "sum", "description": "The total USD value of the transactions that were cancelled.", "create_metric": false, "expr": "CASE WHEN transaction_type_name = 'cancellation' THEN transaction_amount_usd ELSE 0 END", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "alterations_usd", "agg": "sum", "description": "The total USD value of the transactions that were altered.", "create_metric": false, "expr": "CASE WHEN transaction_type_name = 'alteration' THEN transaction_amount_usd ELSE 0 END", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}, {"name": "transacting_customers", "agg": "count_distinct", "description": "The distinct count of customers transacting on any given day.", "create_metric": false, "expr": "id_customer", "agg_params": null, "metadata": null, "non_additive_dimension": null, "agg_time_dimension": null, "label": null}], "dimensions": [{"name": "ds", "description": null, "type": "time", "is_partition": false, "type_params": {"time_granularity": "day", "validity_params": null}, "expr": null, "metadata": null, "label": null}, {"name": "is_large", "description": null, "type": "categorical", "is_partition": false, "type_params": null, "expr": "CASE WHEN transaction_amount_usd >= 30 THEN TRUE ELSE FALSE END", "metadata": null, "label": null}, {"name": "quick_buy_transaction", "description": null, "type": "categorical", "is_partition": false, "type_params": null, "expr": "CASE\n  WHEN transaction_type_name = 'quick-buy' THEN 'Quick Buy'\n  ELSE 'Not Quick Buy'\nEND", "metadata": null, "label": null}], "label": null, "metadata": null, "config": {"meta": {}}}], "metrics": [{"name": "new_customers", "description": "", "type": "simple", "type_params": {"measure": {"name": "new_customers", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "new_customers", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "New Customer", "config": {"meta": {}}, "time_granularity": null}, {"name": "quick_buy_transactions", "description": "", "type": "simple", "type_params": {"measure": {"name": "quick_buy_transactions", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "quick_buy_transactions", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Quick Buy Transactions", "config": {"meta": {}}, "time_granularity": null}, {"name": "quick_buy_amount_usd", "description": "", "type": "simple", "type_params": {"measure": {"name": "quick_buy_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "quick_buy_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Quick Buy Amount (USD)", "config": {"meta": {}}, "time_granularity": null}, {"name": "cancellations", "description": "", "type": "simple", "type_params": {"measure": {"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Cancellations", "config": {"meta": {}}, "time_granularity": null}, {"name": "transactions", "description": "", "type": "simple", "type_params": {"measure": {"name": "transactions", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "transactions", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Transactions", "config": {"meta": {}}, "time_granularity": null}, {"name": "alterations", "description": "", "type": "simple", "type_params": {"measure": {"name": "alterations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "alterations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Alterations", "config": {"meta": {}}, "time_granularity": null}, {"name": "transaction_amount", "description": "", "type": "simple", "type_params": {"measure": {"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Transaction Amount (USD)", "config": {"meta": {}}, "time_granularity": null}, {"name": "cancellation_rate", "description": "", "type": "ratio", "type_params": {"measure": null, "numerator": {"name": "cancellations", "filter": null, "alias": null, "offset_window": null, "offset_to_grain": null}, "denominator": {"name": "transaction_amount", "filter": null, "alias": null, "offset_window": null, "offset_to_grain": null}, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, {"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Cancellation Rate", "config": {"meta": {}}, "time_granularity": null}, {"name": "revenue_usd", "description": "", "type": "derived", "type_params": {"measure": null, "numerator": null, "denominator": null, "expr": "transaction_amount - cancellations + alterations", "window": null, "grain_to_date": null, "metrics": [{"name": "transaction_amount", "filter": null, "alias": null, "offset_window": null, "offset_to_grain": null}, {"name": "cancellations", "filter": null, "alias": null, "offset_window": null, "offset_to_grain": null}, {"name": "alterations", "filter": null, "alias": null, "offset_window": null, "offset_to_grain": null}], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, {"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, {"name": "alterations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": null, "metadata": null, "label": "Revenue (USD)", "config": {"meta": {}}, "time_granularity": null}, {"name": "cancellations_mx", "description": "", "type": "simple", "type_params": {"measure": {"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "cancellations_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": {"where_filters": [{"where_sql_template": "{{ Dimension('customer__customer_country') }} = 'MX'\n"}]}, "metadata": null, "label": "Cancellations in Mexico", "config": {"meta": {}}, "time_granularity": null}, {"name": "transaction_usd_na", "description": "", "type": "simple", "type_params": {"measure": {"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}, "numerator": null, "denominator": null, "expr": null, "window": null, "grain_to_date": null, "metrics": [], "conversion_type_params": null, "cumulative_type_params": null, "input_measures": [{"name": "transaction_amount_usd", "filter": null, "alias": null, "join_to_timespine": false, "fill_nulls_with": null}]}, "filter": {"where_filters": [{"where_sql_template": "{{ Dimension('country__region', entity_path=['customer']) }} = 'NA'\n"}]}, "metadata": null, "label": "Transaction Amount in NA", "config": {"meta": {}}, "time_granularity": null}], "project_configuration": {"time_spine_table_configurations": [], "metadata": null, "dsi_package_version": {"major_version": "0", "minor_version": "7", "patch_version": "4"}, "time_spines": [{"node_relation": {"alias": "all_days", "schema_name": "main", "database": "mf_tutorial", "relation_name": "\"mf_tutorial\".\"main\".\"all_days\""}, "primary_column": {"name": "date_day", "time_granularity": "day"}, "custom_granularities": []}]}, "saved_queries": []}