{"_invocation_id": "d54c12d9-6b68-4a78-9fd9-68009589861b", "linked": {"0": {"name": "source.mf_tutorial_project.tutorial.transactions_seed", "type": "source", "succ": [6]}, "1": {"name": "source.mf_tutorial_project.tutorial.customers_seed", "type": "source", "succ": [4]}, "2": {"name": "source.mf_tutorial_project.tutorial.countries_seed", "type": "source", "succ": [5]}, "3": {"name": "model.mf_tutorial_project.all_days", "type": "model"}, "4": {"name": "model.mf_tutorial_project.customers", "type": "model", "succ": [10]}, "5": {"name": "model.mf_tutorial_project.countries", "type": "model", "succ": [11]}, "6": {"name": "model.mf_tutorial_project.transactions", "type": "model", "succ": [12]}, "7": {"name": "seed.mf_tutorial_project.countries_seed", "type": "seed"}, "8": {"name": "seed.mf_tutorial_project.customers_seed", "type": "seed"}, "9": {"name": "seed.mf_tutorial_project.transactions_seed", "type": "seed"}, "10": {"name": "semantic_model.mf_tutorial_project.customers", "type": "semantic_model", "succ": [13]}, "11": {"name": "semantic_model.mf_tutorial_project.countries", "type": "semantic_model"}, "12": {"name": "semantic_model.mf_tutorial_project.transactions", "type": "semantic_model", "succ": [14, 15, 16, 17, 18, 19, 22, 23]}, "13": {"name": "metric.mf_tutorial_project.new_customers", "type": "metric"}, "14": {"name": "metric.mf_tutorial_project.quick_buy_transactions", "type": "metric"}, "15": {"name": "metric.mf_tutorial_project.quick_buy_amount_usd", "type": "metric"}, "16": {"name": "metric.mf_tutorial_project.cancellations", "type": "metric", "succ": [20, 21]}, "17": {"name": "metric.mf_tutorial_project.transactions", "type": "metric"}, "18": {"name": "metric.mf_tutorial_project.alterations", "type": "metric", "succ": [21]}, "19": {"name": "metric.mf_tutorial_project.transaction_amount", "type": "metric", "succ": [20, 21]}, "20": {"name": "metric.mf_tutorial_project.cancellation_rate", "type": "metric"}, "21": {"name": "metric.mf_tutorial_project.revenue_usd", "type": "metric"}, "22": {"name": "metric.mf_tutorial_project.cancellations_mx", "type": "metric"}, "23": {"name": "metric.mf_tutorial_project.transaction_usd_na", "type": "metric"}}, "with_test_edges": {"0": {"name": "source.mf_tutorial_project.tutorial.transactions_seed", "type": "source", "succ": [6]}, "1": {"name": "source.mf_tutorial_project.tutorial.customers_seed", "type": "source", "succ": [4]}, "2": {"name": "source.mf_tutorial_project.tutorial.countries_seed", "type": "source", "succ": [5]}, "3": {"name": "model.mf_tutorial_project.all_days", "type": "model"}, "4": {"name": "model.mf_tutorial_project.customers", "type": "model", "succ": [10]}, "5": {"name": "model.mf_tutorial_project.countries", "type": "model", "succ": [11]}, "6": {"name": "model.mf_tutorial_project.transactions", "type": "model", "succ": [12]}, "7": {"name": "seed.mf_tutorial_project.countries_seed", "type": "seed"}, "8": {"name": "seed.mf_tutorial_project.customers_seed", "type": "seed"}, "9": {"name": "seed.mf_tutorial_project.transactions_seed", "type": "seed"}, "10": {"name": "semantic_model.mf_tutorial_project.customers", "type": "semantic_model", "succ": [13]}, "11": {"name": "semantic_model.mf_tutorial_project.countries", "type": "semantic_model"}, "12": {"name": "semantic_model.mf_tutorial_project.transactions", "type": "semantic_model", "succ": [14, 15, 16, 17, 18, 19, 22, 23]}, "13": {"name": "metric.mf_tutorial_project.new_customers", "type": "metric"}, "14": {"name": "metric.mf_tutorial_project.quick_buy_transactions", "type": "metric"}, "15": {"name": "metric.mf_tutorial_project.quick_buy_amount_usd", "type": "metric"}, "16": {"name": "metric.mf_tutorial_project.cancellations", "type": "metric", "succ": [20, 21]}, "17": {"name": "metric.mf_tutorial_project.transactions", "type": "metric"}, "18": {"name": "metric.mf_tutorial_project.alterations", "type": "metric", "succ": [21]}, "19": {"name": "metric.mf_tutorial_project.transaction_amount", "type": "metric", "succ": [20, 21]}, "20": {"name": "metric.mf_tutorial_project.cancellation_rate", "type": "metric"}, "21": {"name": "metric.mf_tutorial_project.revenue_usd", "type": "metric"}, "22": {"name": "metric.mf_tutorial_project.cancellations_mx", "type": "metric"}, "23": {"name": "metric.mf_tutorial_project.transaction_usd_na", "type": "metric"}}}