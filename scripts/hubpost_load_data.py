from hubspot import <PERSON>bSpot
from hubspot.crm.contacts import SimplePublicObjectInputForCreate
from hubspot.crm.contacts.exceptions import ApiException


api_client = HubSpot(access_token='pat-na2-************************************')


try:
    simple_public_object_input_for_create = SimplePublicObjectInputForCreate(
        properties={"email": "<EMAIL>"}
    )
    api_response = api_client.crm.contacts.basic_api.create(
        simple_public_object_input_for_create=simple_public_object_input_for_create
    )
except ApiException as e:
    print("Exception when creating contact: %s\n" % e)

