from hubspot import HubSpot
from hubspot.crm.contacts import SimplePublicObjectInputForCreate
from hubspot.crm.companies import SimplePublicObjectInputForCreate as CompanyInput
from hubspot.crm.deals import SimplePublicObjectInputForCreate as DealInput
from hubspot.crm.tickets import SimplePublicObjectInputForCreate as TicketInput
from hubspot.crm.contacts.exceptions import ApiException
from hubspot.crm.companies.exceptions import ApiException as CompanyApiException
from hubspot.crm.deals.exceptions import ApiException as DealApiException
from hubspot.crm.tickets.exceptions import ApiException as TicketApiException
import datetime
import random


api_client = HubSpot(access_token='pat-na2-************************************')

# Sample data for creating multiple entities
contacts_data = [
    {
        "email": "<EMAIL>",
        "firstname": "<PERSON>",
        "lastname": "<PERSON>",
        "phone": "******-0101",
        "jobtitle": "Software Engineer",
        "company": "EtherCap Technologies",
        "website": "ethercap.com",
        "city": "San Francisco",
        "state": "California",
        "country": "United States",
        "lifecyclestage": "customer"
    },
    {
        "email": "<EMAIL>",
        "firstname": "John",
        "lastname": "Doe",
        "phone": "******-0102",
        "jobtitle": "Product Manager",
        "company": "TechCorp Solutions",
        "website": "techcorp.com",
        "city": "Austin",
        "state": "Texas",
        "country": "United States",
        "lifecyclestage": "opportunity"
    },
    {
        "email": "<EMAIL>",
        "firstname": "Sarah",
        "lastname": "Smith",
        "phone": "******-0103",
        "jobtitle": "Sales Director",
        "company": "Innovate Inc",
        "website": "innovate.com",
        "city": "New York",
        "state": "New York",
        "country": "United States",
        "lifecyclestage": "lead"
    },
    {
        "email": "<EMAIL>",
        "firstname": "Mike",
        "lastname": "Johnson",
        "phone": "******-0104",
        "jobtitle": "CTO",
        "company": "Startup.io",
        "website": "startup.io",
        "city": "Seattle",
        "state": "Washington",
        "country": "United States",
        "lifecyclestage": "subscriber"
    },
    {
        "email": "<EMAIL>",
        "firstname": "Anna",
        "lastname": "Garcia",
        "phone": "******-0105",
        "jobtitle": "Senior Consultant",
        "company": "Garcia Consulting",
        "website": "consulting.biz",
        "city": "Miami",
        "state": "Florida",
        "country": "United States",
        "lifecyclestage": "marketingqualifiedlead"
    }
]

companies_data = [
    {
        "name": "EtherCap Technologies",
        "domain": "ethercap.com",
        "industry": "Technology",
        "city": "San Francisco",
        "state": "California",
        "country": "United States",
        "phone": "******-1001",
        "description": "Leading technology solutions provider",
        "numberofemployees": "50",
        "annualrevenue": "5000000"
    },
    {
        "name": "TechCorp Solutions",
        "domain": "techcorp.com",
        "industry": "Software",
        "city": "Austin",
        "state": "Texas",
        "country": "United States",
        "phone": "******-1002",
        "description": "Enterprise software development company",
        "numberofemployees": "200",
        "annualrevenue": "25000000"
    },
    {
        "name": "Innovate Inc",
        "domain": "innovate.com",
        "industry": "Consulting",
        "city": "New York",
        "state": "New York",
        "country": "United States",
        "phone": "******-1003",
        "description": "Business innovation and consulting firm",
        "numberofemployees": "75",
        "annualrevenue": "8000000"
    },
    {
        "name": "Startup.io",
        "domain": "startup.io",
        "industry": "Technology",
        "city": "Seattle",
        "state": "Washington",
        "country": "United States",
        "phone": "******-1004",
        "description": "Innovative startup focused on AI solutions",
        "numberofemployees": "15",
        "annualrevenue": "1000000"
    },
    {
        "name": "Garcia Consulting",
        "domain": "consulting.biz",
        "industry": "Professional Services",
        "city": "Miami",
        "state": "Florida",
        "country": "United States",
        "phone": "******-1005",
        "description": "Strategic business consulting services",
        "numberofemployees": "25",
        "annualrevenue": "3000000"
    }
]

# Create contacts
print("Creating contacts...")
created_contacts = []
for contact_data in contacts_data:
    try:
        simple_public_object_input_for_create = SimplePublicObjectInputForCreate(
            properties=contact_data
        )
        api_response = api_client.crm.contacts.basic_api.create(
            simple_public_object_input_for_create=simple_public_object_input_for_create
        )
        created_contacts.append(api_response)
        print(f"Created contact: {contact_data['email']} with ID: {api_response.id}")
    except ApiException as e:
        print(f"Exception when creating contact {contact_data['email']}: {e}")

# Create companies
print("\nCreating companies...")
created_companies = []
for company_data in companies_data:
    try:
        company_input = CompanyInput(properties=company_data)
        api_response = api_client.crm.companies.basic_api.create(
            simple_public_object_input_for_create=company_input
        )
        created_companies.append(api_response)
        print(f"Created company: {company_data['name']} with ID: {api_response.id}")
    except CompanyApiException as e:
        print(f"Exception when creating company {company_data['name']}: {e}")

# Sample deals data
deals_data = [
    {
        "dealname": "EtherCap Software License",
        "amount": "50000",
        "dealstage": "qualifiedtobuy",
        "pipeline": "default",
        "closedate": "2024-12-31"
    },
    {
        "dealname": "TechCorp Consulting Project",
        "amount": "75000",
        "dealstage": "presentationscheduled",
        "pipeline": "default",
        "closedate": "2024-11-30"
    },
    {
        "dealname": "Innovate Platform Integration",
        "amount": "100000",
        "dealstage": "decisionmakerboughtin",
        "pipeline": "default",
        "closedate": "2025-01-15"
    }
]

# Create deals
print("\nCreating deals...")
created_deals = []
for deal_data in deals_data:
    try:
        deal_input = DealInput(properties=deal_data)
        api_response = api_client.crm.deals.basic_api.create(
            simple_public_object_input_for_create=deal_input
        )
        created_deals.append(api_response)
        print(f"Created deal: {deal_data['dealname']} with ID: {api_response.id}")
    except DealApiException as e:
        print(f"Exception when creating deal {deal_data['dealname']}: {e}")

# Sample tickets data
tickets_data = [
    {
        "subject": "Software Installation Issue",
        "content": "Customer experiencing issues with software installation on Windows 10",
        "hs_pipeline": "0",
        "hs_pipeline_stage": "1",
        "hs_ticket_priority": "MEDIUM"
    },
    {
        "subject": "Feature Request - API Integration",
        "content": "Customer requesting new API endpoints for third-party integration",
        "hs_pipeline": "0",
        "hs_pipeline_stage": "1",
        "hs_ticket_priority": "LOW"
    },
    {
        "subject": "Billing Inquiry",
        "content": "Question about recent invoice and payment terms",
        "hs_pipeline": "0",
        "hs_pipeline_stage": "2",
        "hs_ticket_priority": "HIGH"
    }
]

# Create tickets
print("\nCreating tickets...")
created_tickets = []
for ticket_data in tickets_data:
    try:
        ticket_input = TicketInput(properties=ticket_data)
        api_response = api_client.crm.tickets.basic_api.create(
            simple_public_object_input_for_create=ticket_input
        )
        created_tickets.append(api_response)
        print(f"Created ticket: {ticket_data['subject']} with ID: {api_response.id}")
    except TicketApiException as e:
        print(f"Exception when creating ticket {ticket_data['subject']}: {e}")

# Create notes/activities for contacts
print("\nCreating notes and activities...")
if created_contacts:
    for i, contact in enumerate(created_contacts):
        try:
            # Create a note
            note_properties = {
                "hs_note_body": f"Initial contact note for {contacts_data[i]['firstname']} {contacts_data[i]['lastname']}. Contact created via API.",
                "hs_timestamp": str(int(datetime.datetime.now().timestamp() * 1000))
            }

            # Create note using engagements API
            note_data = {
                "engagement": {
                    "active": True,
                    "type": "NOTE"
                },
                "associations": {
                    "contactIds": [int(contact.id)]
                },
                "metadata": note_properties
            }

            # Note: This would require the engagements API which might have different import
            print(f"Would create note for contact {contact.id}")

        except Exception as e:
            print(f"Exception when creating note for contact {contact.id}: {e}")

# Create associations between contacts, companies, and deals
print("\nCreating associations...")
if created_contacts and created_companies and created_deals:
    try:
        # Associate first contact with first company
        if len(created_contacts) > 0 and len(created_companies) > 0:
            association_data = {
                "from_object_id": created_contacts[0].id,
                "to_object_id": created_companies[0].id,
                "association_type": "contact_to_company"
            }
            print(f"Would associate contact {created_contacts[0].id} with company {created_companies[0].id}")

        # Associate first contact with first deal
        if len(created_contacts) > 0 and len(created_deals) > 0:
            association_data = {
                "from_object_id": created_contacts[0].id,
                "to_object_id": created_deals[0].id,
                "association_type": "contact_to_deal"
            }
            print(f"Would associate contact {created_contacts[0].id} with deal {created_deals[0].id}")

        # Associate first company with first deal
        if len(created_companies) > 0 and len(created_deals) > 0:
            association_data = {
                "from_object_id": created_companies[0].id,
                "to_object_id": created_deals[0].id,
                "association_type": "company_to_deal"
            }
            print(f"Would associate company {created_companies[0].id} with deal {created_deals[0].id}")

    except Exception as e:
        print(f"Exception when creating associations: {e}")

print("\nData creation completed!")
print(f"Created {len(created_contacts)} contacts")
print(f"Created {len(created_companies)} companies")
print(f"Created {len(created_deals)} deals")
print(f"Created {len(created_tickets)} tickets")

# Print summary of created objects
print("\n=== CREATED OBJECTS SUMMARY ===")
print("\nContacts:")
for i, contact in enumerate(created_contacts):
    print(f"  - {contacts_data[i]['firstname']} {contacts_data[i]['lastname']} (ID: {contact.id})")

print("\nCompanies:")
for i, company in enumerate(created_companies):
    print(f"  - {companies_data[i]['name']} (ID: {company.id})")

print("\nDeals:")
for i, deal in enumerate(created_deals):
    print(f"  - {deals_data[i]['dealname']} - ${deals_data[i]['amount']} (ID: {deal.id})")

print("\nTickets:")
for i, ticket in enumerate(created_tickets):
    print(f"  - {tickets_data[i]['subject']} (ID: {ticket.id})")

