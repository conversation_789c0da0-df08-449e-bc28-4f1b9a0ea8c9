from datetime import timedelta
from tortoise import Tortoise, connections
from itertools import pairwise

async def init():
    await Tortoise.init(
        db_url='asyncpg://salestech_be:salestech_be@localhost:5432/salestech_be',
        # db_url='asyncpg://reevo_db_user:<EMAIL>:5432/reevo_main',
        # db_url='asyncpg://reevo_db_user:<EMAIL>:5432/reevo_main',
        modules={'models': ['models']}
    )


async def fix_pipeline_tracking():
    conn = connections.get("default")

    sql = """
        SELECT DISTINCT t.pipeline_id
        FROM pipeline_tracking t
        WHERE t.field_name = 'stage_id' 
        GROUP BY t.pipeline_id, t.created_at
        HAVING COUNT(t.id) > 1
    """
    for pipeline in await conn.execute_query_dict(sql):
        pipeline_id = pipeline["pipeline_id"]
        timedelta_offset = 0
        print(pipeline_id)
        sql = """
            SELECT t.id, t.created_at
            FROM pipeline_tracking t
            WHERE t.field_name = 'stage_id' 
            AND t.pipeline_id = '{pipeline_id}'
            ORDER BY t.created_at, t.ctid
        """
        trackings = await conn.execute_query_dict(sql)
        
        # Iterate through adjacent pairs to find same timestamps
        for current, next_tracking in pairwise(trackings):
            # If timestamps match, add 1 microsecond to the later tracking
            if current['created_at'] == next_tracking['created_at']:
                timedelta_offset += 1
                new_created_at = next_tracking['created_at'] + timedelta(microseconds=timedelta_offset)
                print(pipeline_id, next_tracking['id'], next_tracking['created_at'], new_created_at)
                await conn.execute_query(
                    f"UPDATE pipeline_tracking SET created_at = '{new_created_at}' WHERE id = '{next_tracking.id}'"
                )
            else:
                timedelta_offset = 0


async def main():
    await init()
    await fix_pipeline_tracking()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
