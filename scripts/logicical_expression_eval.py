from __future__ import annotations

from typing import Any, Literal, Union

from pydantic import BaseModel, Field

OperandValue = str | int | float | bool | None


class VariableOperand(BaseModel):
    type: Literal["Variable"] = "Variable"
    value: OperandValue


class ConstantOperand(BaseModel):
    type: Literal["Constant"] = "Constant"
    value: OperandValue


Operand = VariableOperand | ConstantOperand


class BaseExpression(BaseModel):
    pass


class And(BaseExpression):
    type: Literal["And"] = "And"
    expressions: list[Expression] = Field(..., min_items=2)


class Or(BaseExpression):
    type: Literal["Or"] = "Or"
    expressions: list[Expression] = Field(..., min_items=2)


class IsEqual(BaseExpression):
    type: Literal["IsEqual"] = "IsEqual"
    left_value: Operand
    right_value: Operand


class IsNotEqual(BaseExpression):
    type: Literal["IsNotEqual"] = "IsNotEqual"
    left_value: Operand
    right_value: Operand


Expression = Union[And, Or, IsEqual, IsNotEqual]


class RootExpression(BaseModel):
    expression: Expression


def evaluate_operand(operand: Operand, parameters: dict) -> Any:
    if isinstance(operand, VariableOperand):
        return parameters.get(operand.value)
    elif isinstance(operand, ConstantOperand):
        return operand.value
    else:
        raise ValueError("Invalid operand")


def evaluate_expression(expression: Expression, parameters: dict) -> bool:
    if isinstance(expression, And):
        return all(
            evaluate_expression(expr, parameters) for expr in expression.expressions
        )
    elif isinstance(expression, Or):
        return any(
            evaluate_expression(expr, parameters) for expr in expression.expressions
        )
    elif isinstance(expression, IsEqual):
        left = evaluate_operand(expression.left_value, parameters)
        right = evaluate_operand(expression.right_value, parameters)
        return left == right
    elif isinstance(expression, IsNotEqual):
        left = evaluate_operand(expression.left_value, parameters)
        right = evaluate_operand(expression.right_value, parameters)
        return left != right
    else:
        raise ValueError("Invalid expression")


if __name__ == "__main__":
    parameters = {"x": 5, "y": 2, "z": True}
    # (x == 5)
    simple_expression = {
        "type": "IsEqual",
        "left_value": {"type": "Variable", "value": "x"},
        "right_value": {"type": "Constant", "value": 5},
    }

    # (x == 4) OR (y == 1) OR (y != null AND (z == true OR z == 2 AND y == 3)) .
    complex_expression = {
        "type": "Or",
        "expressions": [
            {
                "type": "IsEqual",
                "left_value": {"type": "Variable", "value": "x"},
                "right_value": {"type": "Variable", "value": 4},
            },
            {
                "type": "IsEqual",
                "left_value": {"type": "Variable", "value": "y"},
                "right_value": {"type": "Variable", "value": 1},
            },
            {
                "type": "And",
                "expressions": [
                    {
                        "type": "IsNotEqual",
                        "left_value": {"type": "Variable", "value": "y"},
                        "right_value": {"type": "Variable", "value": None},
                    },
                    {
                        "type": "Or",
                        "expressions": [
                            {
                                "type": "IsEqual",
                                "left_value": {"type": "Variable", "value": "z"},
                                "right_value": {"type": "Constant", "value": True},
                            },
                            {
                                "type": "And",
                                "expressions": [
                                    {
                                        "type": "IsEqual",
                                        "left_value": {
                                            "type": "Variable",
                                            "value": "z",
                                        },
                                        "right_value": {"type": "Variable", "value": 2},
                                    },
                                    {
                                        "type": "IsEqual",
                                        "left_value": {
                                            "type": "Variable",
                                            "value": "y",
                                        },
                                        "right_value": {"type": "Variable", "value": 3},
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
    }
    expressions = [
        simple_expression,
        complex_expression,
    ]
    for expression in expressions:
        root_expression = {"expression": expression}
        expression_model = RootExpression.model_validate(root_expression)
        print(expression_model)
        print(expression_model.model_dump_json(indent=2))
        print(evaluate_expression(expression_model.expression, parameters))