#!/usr/bin/env python3

"""
CLI wrapper for salestech_be API operations.

1. You will want to ensure the terminal you are running this script from has `env-up` running.
2. You will want to ensure PATH is set to include the absolute path to the `scripts/cli` directory, at the last position of PATH.
   e.g. `cd <path to salestech_be REPOROOT> ; export PATH=$PATH:$(pwd)/scripts/cli`

Usages:
    cURL an API endpoint, with the Authorization header set (using JWT generation), and template-rendering:
        reevo curl <URL> -- <curl options>

        e.g.
        reevo curl "http://localhost:8000/api/v1/organization/{organization_id}/backfill_permissions" -- --request POST

    Generate a JWT for a user:
        reevo token generate

Setup if you do not want to specify the --user-id and --org-id each time:

    `.env.cli` contents:
    LOCAL_REEVO_USER_ID="3b2f46a7-e4b3-42f1-a7f4-a232eb5dcfaa"
    LOCAL_REEVO_ORG_ID="cce7b290-8a08-4904-a6c7-2b6613877cf5"
"""

import argparse
import asyncio
import os
import sys
import textwrap
import time
import uuid
from shlex import quote

from dotenv import dotenv_values

from salestech_be.ree_logging import get_logger

logger = get_logger(__name__)
logger.remove()  # Disable logging for this script.

from salestech_be.core.user.service.permission_service import (  # noqa: E402
    get_permission_service_by_db_engine,
)
from salestech_be.services.auth.tokens import create_access_token  # noqa: E402
from salestech_be.services.auth.types import ReevoJWTClaims  # noqa: E402
from salestech_be.temporal.database import get_or_init_db_engine  # noqa: E402

FOUR_HOURS_FROM_NOW = time.time() + (4 * 60 * 60)
ONE_MINUTE_FROM_NOW = time.time() + 60  # just in case the application is hot-reloading.

config = dotenv_values(".env.cli")


async def generate_jwt(
    args: argparse.Namespace, token_expiry: float = FOUR_HOURS_FROM_NOW
) -> str:
    user_id = uuid.UUID(quote(args.user_id))
    organization_id = uuid.UUID(quote(args.org_id))

    db_engine = await get_or_init_db_engine()
    perm_service = get_permission_service_by_db_engine(db_engine)
    user_permission_claims = await perm_service.get_user_permission_claims(
        user_id=user_id,
        organization_id=organization_id,
    )

    return create_access_token(
        claims=ReevoJWTClaims(
            sub=user_id,
            org=organization_id,
            exp=token_expiry,
            iat=time.time(),
            perm=user_permission_claims,
        )
    )


async def curl_call(args: argparse.Namespace) -> str:
    """
    Wrap an API endpoint for use in a curl command, injecting the Authorization header for convenience.

    Will attempt to recognize certain placeholders in the endpoint URL, and replace them with the correct values.
    """
    token = await generate_jwt(args, token_expiry=ONE_MINUTE_FROM_NOW)
    api_endpoint = args.endpoint.format(
        organization_id=args.org_id,
    )

    curl_cmd = f"""
curl --verbose \
-H "x-reevo-user-id: {quote(args.user_id)}" \
-H "x-reevo-org-id: {quote(args.org_id)}" \
-H "Authorization: Bearer {token}" \
{quote(api_endpoint)}
    """

    # Make it easier to copy-paste the command for debugging.
    curl_cmd = (
        textwrap.dedent(curl_cmd.strip())
        + " "
        + " ".join(quote(arg) for arg in args.curlopts)
    )
    sys.stdout.write(curl_cmd + os.linesep)

    proc = await asyncio.create_subprocess_shell(curl_cmd)
    sys.exit(proc.returncode)


async def main() -> None:
    # ==============================
    # Setup argparse.

    # command: rev
    parser = argparse.ArgumentParser(
        prog="reevo", description="CLI wrapper for Reevo operations"
    )
    parser.add_argument(
        "--user-id", type=str, default=config.get("LOCAL_REEVO_USER_ID")
    )
    parser.add_argument("--org-id", type=str, default=config.get("LOCAL_REEVO_ORG_ID"))
    subparsers = parser.add_subparsers()

    # subcommand: rev curl
    curl_parser = subparsers.add_parser("curl", help="CURL operations")
    curl_parser.add_argument("endpoint", type=str)
    curl_parser.add_argument("curlopts", nargs=argparse.REMAINDER)
    curl_parser.set_defaults(func=curl_call)

    # subcommand: rev token
    token_parser = subparsers.add_parser("token", help="JWT operations")

    # subcommand: rev token generate
    token_subparsers = token_parser.add_subparsers()
    token_generate_parser = token_subparsers.add_parser(
        "generate", help="Generate a JWT for a user"
    )
    token_generate_parser.set_defaults(func=generate_jwt)

    # ==============================
    # Parse, and run the target command.

    args = parser.parse_args()

    if not args.user_id or not args.org_id:
        parser.error("Both --user-id and --org-id are required")

    # Get the `func` handler, or show the USAGE.
    func = getattr(args, "func", None)
    if not func:
        parser.print_help()
        sys.exit(1)
    output = await func(args)
    if output:
        sys.stdout.write(output)


if __name__ == "__main__":
    asyncio.run(main())
