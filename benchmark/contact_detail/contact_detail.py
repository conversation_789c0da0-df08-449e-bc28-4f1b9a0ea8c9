from locust import <PERSON><PERSON>ttpUser, task
from uuid import UUID
import random
from gevent.pool import Group
from datetime import datetime, timezone

"""
This mocks the real user's behavior to load the contact detail page.

When the page is loaded, the user will send a series of requests to get the data of the contact detail page.

The requests are:
1. list_contact
2. list_task
3. list_activity
4. list_pipeline
5. list_account
6. list_upcoming_meetings
7. list_past_conversations

The requests are sent in parallel.
"""


class ContactDetailUser(FastHttpUser):
    contact_id = None
    user_id = UUID("2a9e557e-493d-4b0b-8a91-b2d0475aa952")
    organization_id = UUID("cce7b290-8a08-4904-a6c7-2b6613877cf5")
    network_timeout = 20

    def get_contact_id(self):
        if not self.contact_id:
            with open("contact_ids.txt", "r") as f:
                contact_ids = f.readlines()
            self.contact_id = UUID(random.choice(contact_ids).strip())
        return self.contact_id

    def send_req_list_contact(self, contact_id):
        self.client.post(
            "/api/v1/contacts/_list",
            name="list_contact(/api/v1/contacts/_list)",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "record_id": str(contact_id),
                "ordered_object_fields": [
                    {
                        "path": ["id"],
                        "fetch_relationship_ids": ["contact__to__primary_account"],
                    },
                    {
                        "path": ["owner_user_id"],
                        "fetch_relationship_ids": ["contact__to__owner_user"],
                    },
                ],
                "include_custom_field_data": True,
            },
        )

    def send_req_list_task(self, contact_id):
        self.client.post(
            "/api/v1/tasks/_list",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "filter_spec": {
                    "filter": {
                        "filter_type": "COMPOSITE",
                        "all_of": [
                            {
                                "filter_type": "COMPOSITE",
                                "any_of": [
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["contact_ids"]},
                                        "operator": "CONTAINS",
                                        "value": str(contact_id),
                                    }
                                ],
                            },
                            {
                                "filter_type": "VALUE",
                                "field": {"path": ["status"]},
                                "operator": "NIN",
                                "value": ["COMPLETED", "CLOSED"],
                            },
                        ],
                    },
                    "primary_object_identifier": {
                        "object_name": "task",
                        "object_kind": "STANDARD",
                    },
                },
                "ordered_object_fields": [
                    {
                        "path": ["id"],
                        "fetch_relationship_ids": [
                            "task__to__owner_user",
                            "task__to__pipeline",
                            "task__to__account",
                            "task__to__contacts",
                        ],
                    }
                ],
            },
        )

    def send_req_list_activity(self, contact_id):
        self.client.post(
            "/api/v1/activities/_list",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "filters": [
                    {
                        "field": "contact_id",
                        "value": [str(contact_id)],
                        "operator": "or",
                        "predicate": "in",
                    }
                ],
                "cursor": {"page_index": 1, "page_size": 50},
            },
        )

    def send_req_list_pipeline(self, contact_id):
        self.client.post(
            "/api/v1/contacts/_list",
            name="list_pipeline(/api/v1/contacts/_list)",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "record_id": str(contact_id),
                "ordered_object_fields": [
                    {
                        "field": {
                            "field": {"path": ["id"]},
                            "relationship_id": "pipeline__to__account",
                        },
                        "relationship_id": "primary_contact__from__pipeline",
                    },
                    {
                        "field": {
                            "field": {"path": ["id"]},
                            "relationship_id": "pipeline__to__account",
                        },
                        "relationship_id": "additional_contact__from__pipeline",
                    },
                ],
                "include_custom_field_data": True,
            },
        )

    def send_req_list_account(self, contact_id):
        self.client.post(
            "/api/v1/contacts/_list",
            name="list_account(/api/v1/contacts/_list)",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "record_id": str(contact_id),
                "ordered_object_fields": [
                    {
                        "path": ["id"],
                        "fetch_relationship_ids": ["contact__to__primary_account"],
                    },
                    {
                        "path": ["owner_user_id"],
                        "fetch_relationship_ids": ["contact__to__owner_user"],
                    },
                ],
                "include_custom_field_data": True,
            },
        )

    def send_req_list_upcoming_meetings(self, contact_id):
        self.client.post(
            "/api/v1/meetings/_list",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "filter_spec": {
                    "primary_object_identifier": {
                        "object_kind": "STANDARD",
                        "object_name": "meeting",
                    },
                    "filter": {
                        "filter_type": "COMPOSITE",
                        "all_of": [
                            {
                                "filter_type": "VALUE",
                                "field": {
                                    "relationship_id": "meeting__to__invitee_contact",
                                    "field": {"path": ["id"]},
                                },
                                "operator": "IN",
                                "value": [str(contact_id)],
                            },
                            {
                                "filter_type": "COMPOSITE",
                                "any_of": [
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["starts_at"]},
                                        "operator": "GT",
                                        "value": datetime.now(timezone.utc).isoformat(
                                            timespec="milliseconds"
                                        )
                                        + "Z",
                                    },
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["meeting_status"]},
                                        "operator": "IN",
                                        "value": ["active"],
                                    },
                                ],
                            },
                        ],
                        "none_of": [
                            {
                                "filter_type": "COMPOSITE",
                                "all_of": [
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["meeting_platform"]},
                                        "operator": "EQ",
                                        "value": "voice",
                                    },
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["is_recorded"]},
                                        "operator": "EQ",
                                        "value": True,
                                    },
                                ],
                            }
                        ],
                    },
                },
                "sorting_spec": {
                    "primary_object_identifier": {
                        "object_kind": "STANDARD",
                        "object_name": "meeting",
                    },
                    "ordered_sorters": [
                        {"field": {"path": ["starts_at"]}, "order": "asc"}
                    ],
                },
                "ordered_object_fields": [
                    {
                        "path": ["attendee_user_id_list"],
                        "fetch_relationship_ids": ["meeting__to__attendee_user"],
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__invitee_user"],
                        "path": ["invitee_user_id_list"],
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__invitee_contact"],
                        "path": ["invitee_contact_id_list"],
                    },
                    {
                        "relationship_id": "meeting__to__invitee_contact",
                        "field": {
                            "relationship_id": "contact__to__primary_account",
                            "field": {"path": ["id"]},
                        },
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__attendee_contact"],
                        "path": ["attendee_contact_id_list"],
                    },
                ],
                "cursor": {"page_index": 1, "page_size": 100},
            },
        )

    def send_req_list_past_conversations(self, contact_id):
        self.client.post(
            "/api/v1/conversations/_list",
            headers={
                "x-reevo-user-id": str(self.user_id),
                "x-reevo-org-id": str(self.organization_id),
            },
            json={
                "filter_spec": {
                    "primary_object_identifier": {
                        "object_kind": "STANDARD",
                        "object_name": "meeting",
                    },
                    "filter": {
                        "filter_type": "COMPOSITE",
                        "all_of": [
                            {
                                "filter_type": "VALUE",
                                "field": {
                                    "relationship_id": "meeting__to__invitee_contact",
                                    "field": {"path": ["id"]},
                                },
                                "operator": "IN",
                                "value": [str(contact_id)],
                            },
                            {
                                "filter_type": "VALUE",
                                "field": {"path": ["meeting_status"]},
                                "operator": "IN",
                                "value": ["completed", "analyzing", "ended"],
                            },
                            {
                                "filter_type": "COMPOSITE",
                                "any_of": [
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["starts_at"]},
                                        "operator": "LT",
                                        "value": datetime.now(timezone.utc).isoformat(
                                            timespec="milliseconds"
                                        )
                                        + "Z",
                                    }
                                ],
                            },
                        ],
                        "none_of": [
                            {
                                "filter_type": "COMPOSITE",
                                "all_of": [
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["meeting_platform"]},
                                        "operator": "EQ",
                                        "value": "voice",
                                    },
                                    {
                                        "filter_type": "VALUE",
                                        "field": {"path": ["is_recorded"]},
                                        "operator": "EQ",
                                        "value": False,
                                    },
                                ],
                            }
                        ],
                    },
                },
                "sorting_spec": {
                    "primary_object_identifier": {
                        "object_kind": "STANDARD",
                        "object_name": "meeting",
                    },
                    "ordered_sorters": [
                        {"field": {"path": ["starts_at"]}, "order": "desc"}
                    ],
                },
                "ordered_object_fields": [
                    {
                        "path": ["attendee_user_id_list"],
                        "fetch_relationship_ids": ["meeting__to__attendee_user"],
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__invitee_user"],
                        "path": ["invitee_user_id_list"],
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__invitee_contact"],
                        "path": ["invitee_contact_id_list"],
                    },
                    {
                        "relationship_id": "meeting__to__invitee_contact",
                        "field": {
                            "relationship_id": "contact__to__primary_account",
                            "field": {"path": ["id"]},
                        },
                    },
                    {
                        "fetch_relationship_ids": ["meeting__to__attendee_contact"],
                        "path": ["attendee_contact_id_list"],
                    },
                ],
                "cursor": {"page_index": 1, "page_size": 100},
            },
        )

    @task
    def get_contact_detail(self):
        contact_id = self.get_contact_id()
        group = Group()
        group.spawn(self.send_req_list_contact(contact_id))
        group.spawn(self.send_req_list_task(contact_id))
        group.spawn(self.send_req_list_activity(contact_id))
        group.spawn(self.send_req_list_pipeline(contact_id))
        group.spawn(self.send_req_list_account(contact_id))
        group.spawn(self.send_req_list_upcoming_meetings(contact_id))
        group.spawn(self.send_req_list_past_conversations(contact_id))
        group.join()
