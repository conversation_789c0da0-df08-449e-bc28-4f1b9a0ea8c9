[project]
name = "reevo-ng"
description = ""
authors = []
maintainers = []
readme = "README.md"
version = "0.1.0"
requires-python = ">=3.12,<3.13"
dependencies = [
    "tortoise-orm[asyncpg] (>=0.24.0,<0.25.0)",
    "pydantic (>=2.10.6,<3.0.0)",
    "locust (>=2.32.9,<3.0.0)",
    "fastapi[standard] (>=0.115.8,<0.116.0)",
    "pytest (>=8.3.4,<9.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "pytest-asyncio (>=0.25.3,<0.26.0)",
    "orjson (>=3.10.15,<4.0.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "hubspot-api-client (>=11.1.0,<12.0.0)",
    "elasticsearch (>=8.17.1,<9.0.0)",
    "aiohttp (>=3.11.12,<4.0.0)",
    "dbt-metricflow (>=0.8.1,<0.9.0)",
    "graphviz (>=0.20.3,<0.21.0)",
    "metricflow (>=0.207.2,<0.208.0)",
    "dbt-sqlite (>=1.9.0,<2.0.0)",
    "dbt-postgres (>=1.9.0,<2.0.0)",
    "dbt-core (>=1.9.2,<2.0.0)",
    "duckdb (>=1.2.0,<2.0.0)",
    "dbt-materialize (>=1.9.3,<2.0.0)",
    "psycopg[binary] (>=3.2.5,<4.0.0)",
    "sqlalchemy (>=2.0.38,<3.0.0)",
    "psycopg-pool (>=3.2.6,<4.0.0)",
    "dbt-duckdb (>=1.9.2,<2.0.0)",
    "sqlmesh (>=0.188.1,<0.189.0)"
]

[tool.poetry]
requires-poetry = ">=2.0"
